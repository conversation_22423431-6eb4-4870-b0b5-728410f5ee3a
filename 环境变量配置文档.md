# 环境变量配置文档

## 1. 环境变量配置

逻辑处理服务通过环境变量进行配置。在启动服务前，必须在运行环境中提供这些变量。建议将这些变量存储在部署目录下的一个 `.env` 文件中，并通过Docker Compose等工具注入到服务容器内。

| 变量名 (Variable Name) | 示例值 (Example Value) | 说明 (Description) |
| --- | --- | --- |
| **通用配置** |  |  |
| `NODE_ENV` | `development` | 运行环境。可选值为 `development` (开发环境) 或 `production` (生产环境)。 |
| `PORT` | `3000` | 服务监听的HTTP端口，用于接收API请求（如EMQX Webhook）。 |
| `API_AUTH_TOKEN` | `a8db9ce5-fbe9-41e8-80b0-6f7a9bb81318` | 用于保护内部管理API（如人工裁判终端、后台管理）的静态Bearer Token。 |
| **NocoDB 配置** |  |  |
| `NOCODB_API_BASE_URL` | `https://noco.ohvfx.com` | NocoDB实例的API基础地址。 |
| `NOCODB_API_TOKEN` | `bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp` | 从NocoDB项目设置中生成的API访问令牌 (`xc-token`)。 |
| **Redis 配置** |  |  |
| `REDIS_URL` | `redis://default:<EMAIL>:49564` | Redis实例的连接字符串。 |
| **EMQX (MQTT Broker) 配置** |  |  |
| `MQTT_BROKER_URL` | `wss://ws.ohvfx.com:8084/mqtt` | EMQX Broker的WebSocket连接地址。 |
| `MQTT_USERNAME` | `1001` | 逻辑服务连接MQTT Broker所使用的用户名。 |
| `MQTT_PASSWORD` | `1001` | 上述用户名对应的密码。 |