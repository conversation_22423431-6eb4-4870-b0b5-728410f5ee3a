# 环境变量配置文档

## 概述

逻辑处理服务通过环境变量进行配置。在启动服务前，必须在运行环境中提供这些变量。建议将这些变量存储在部署目录下的 `.env` 文件中，并通过 Docker Compose 等工具注入到服务容器内。

## 环境变量配置

### 通用配置

| 变量名 | 类型 | 必需 | 默认值 | 示例值 | 说明 |
|--------|------|------|--------|--------|------|
| `NODE_ENV` | string | 是 | - | `development` | 运行环境。可选值：`development`（开发环境）、`production`（生产环境） |
| `PORT` | number | 否 | 3000 | `3000` | 服务监听的 HTTP 端口，用于接收 API 请求（如 EMQX Webhook） |
| `API_AUTH_TOKEN` | string | 是 | - | `a8db9ce5-fbe9-41e8-80b0-6f7a9bb81318` | 用于保护内部管理 API（如人工裁判终端、后台管理）的静态 Bearer Token |

### NocoDB 配置

| 变量名 | 类型 | 必需 | 默认值 | 示例值 | 说明 |
|--------|------|------|--------|--------|------|
| `NOCODB_API_BASE_URL` | string | 是 | - | `https://noco.ohvfx.com` | NocoDB 实例的 API 基础地址 |
| `NOCODB_API_TOKEN` | string | 是 | - | `bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp` | 从 NocoDB 项目设置中生成的 API 访问令牌（`xc-token`） |

### Redis 配置

| 变量名 | 类型 | 必需 | 默认值 | 示例值 | 说明 |
|--------|------|------|--------|--------|------|
| `REDIS_URL` | string | 是 | - | `redis://default:<EMAIL>:49564` | Redis 实例的连接字符串 |

### EMQX (MQTT Broker) 配置

| 变量名 | 类型 | 必需 | 默认值 | 示例值 | 说明 |
|--------|------|------|--------|--------|------|
| `MQTT_BROKER_URL` | string | 是 | - | `wss://ws.ohvfx.com:8084/mqtt` | EMQX Broker 的 WebSocket 连接地址 |
| `MQTT_USERNAME` | string | 是 | - | `1001` | 逻辑服务连接 MQTT Broker 所使用的用户名 |
| `MQTT_PASSWORD` | string | 是 | - | `1001` | 上述用户名对应的密码 |