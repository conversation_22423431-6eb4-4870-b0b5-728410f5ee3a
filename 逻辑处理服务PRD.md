# 逻辑处理服务 (Logic Handler Service) - 产品需求文档 (PRD)

| **文档版本** | **日期** | **作者** | **修订说明** |
| --- | --- | --- | --- |
| V1.1 | 2025年7月21日 | Gemini AI | 在V1.0基础上增加同步请求接口(HTTP API)板块 |
| V1.0 | 2025年7月21日 | Gemini AI | 初始版本，根据技术选型和Topic设计创建 |

---

### **1. 引言 (Introduction)**

### **1.1. 项目背景**

本项目旨在为一套功能完善的实时赛事系统构建核心的后端“大脑”——逻辑处理服务。该服务是整个系统的业务逻辑核心，负责接收前端（中控、选手、评委等）通过消息中枢（EMQX）发送的指令和事件，执行所有业务规则（如计分、计时、流程控制），并更新系统状态，最终将结果反馈给所有相关方。

### **1.2. 产品目标**

- **实现业务逻辑处理：** 成为赛事规则、计分逻辑、流程控制的唯一执行者。
- **保障系统实时性：** 快速响应前端操作，低延迟处理消息，确保流畅的赛事体验。
- **维护数据一致性：** 可靠地管理赛事的实时状态（存入Redis）和历史数据（存入NocoDB）。
- **提供高可靠性服务：** 保证服务稳定运行，具备良好的错误处理和恢复能力。

### **1.3. 目标用户**

本文档主要面向以下人员：

- **后端开发工程师：** 作为服务实现的主要依据。
- **测试工程师：** 作为编写测试用例和进行功能验证的依据。
- **产品经理/项目负责人：** 用于明确产品范围和功能细节。
- **运维工程师：** 用于理解服务的部署和监控需求。

---

### **2. 系统架构与定位 (System Architecture & Positioning)**

本服务是五层赛事系统架构中的第三层，其在系统中的数据流转位置如下：

`前端应用层 -> EMQX 实时消息中枢 -> [逻辑处理服务] -> Redis/NocoDB -> EMQX 实时消息中枢 -> 前端应用层`

- **上游：** 通过订阅 **EMQX Broker** 的特定 Topic 来接收所有指令和事件。
- **下游：**
    - 将高频读写的**实时状态**存入 **Redis**。
    - 将需要永久保存的**历史记录**写入 **NocoDB（使用nocodb-sdk）**。
    - 将处理后的**结果**发布回 **EMQX Broker**，以供前端应用层消费。

**选定技术栈：**

- **语言/运行时:** Node.js (使用 TypeScript)
- **框架:** **Nest.js** ，并选用 **Fastify** 作为底层 HTTP 服务器以追求更高性能。

本系统所有数据交换（包括 MQTT Payload 和 HTTP API Body）的 JSON 字段均遵循蛇形命名法 (`snake_case`)

---

### **3. 核心职责 (Core Responsibilities)**

- **消息订阅与路由：** 订阅所有 `command` 和 `event` 域的 Topic，并根据 Topic 结构路由到相应的处理逻辑。
- **指令执行：** 处理来自中控端的各种控制指令，如开始环节、切换题目、控制计时器等。
- **事件处理：** 处理来自选手端、评委端的用户事件，如提交答案、提交评分。
- **状态管理：** 维护 Redis 中的实时状态数据，包括但不限于各队得分、排名、答题进度、当前环节/题目等。
- **数据持久化：** 将每一条原始事件和关键处理结果作为日志存入 NocoDB，形成不可变的审计记录。
- **结果广播：** 将业务逻辑处理后的结果（如得分、排名更新、状态变更）发布到 `result` 域的 Topic，通知所有客户端。

---

### **4. 功能性需求 (Functional Requirements)**

本服务必须实现对 Topic 设计文档中所有 `command` 和 `event` 域消息的处理。本节将首先概述核心的消息接收机制和两大核心数据流转流程，随后分类详述对各类 Topic 的具体处理逻辑。

### **4.1. 消息接收机制**

服务启动后，应使用 MQTT 客户端订阅以下通配符 Topic：

- `+/command/#`
- `+/event/#`
- `system/bootstrap/assign_event`

### **4.2. 核心数据流转 (Core Data Flows)**

本系统的数据流转根据题目是否可以被机器自动判断，分为两条清晰的路径。但所有路径都遵循我们确立的核心原则，最终会汇合到统一的状态更新和结果广播流程中，以确保系统的高度一致性和健壮性。

### **4.2.1. 自动判题流程 (客观题，如选择题)**

此流程追求极致的性能和自动化，适用于选择、判断等可以被系统直接判定正误的题目。

1. **(赛前) 答案预加载**
    - **动作**: 在环节开始前，逻辑处理服务从 NocoDB `题目表`中读取本环节所有题目的 `question_id`、`correct_answer`、`points` 以及 `question_type`。
    - **目的**: 使用 `event:{eventid}:questions` 这个单一、集中的 Hash 来缓存所有题目信息。**`question_type` 是后续判断采用自动判分还是人工判分流程的核心依据**。
    - **优势**: 避免在高速判题时频繁请求磁盘数据库，确保判题延迟在毫秒级。
2. **提交 (事件)**
    - **动作**: **选手端**将包含答案的 JSON 数据发布到 EMQX 的 `{eventid}/event/answer/submit` 主题。
    - **数据**: 严格遵循《Topic 设计》规范，Payload 字段采用 `snake_case` 格式。JSON
        
        `{
          "user_id": 1001,
          "session_id": "qa_mandatory_01",
          "question_id": 281,
          "question_number": 1,
          "submitted_answer": "D"
        }`
        
3. **触发**
    - **动作**: EMQX 的规则引擎监听到 `{eventid}/event/answer/submit` 主题的消息，通过 Webhook 功能，向**逻辑处理服务**的 HTTP API (例如 `POST /api/v1/hooks/emqx`) 发起一个同步请求。
4. **处理**
- **动作1**: **逻辑处理服务**收到 HTTP 请求后，在执行任何业务操作之前，必须执行以下**前置校验**：
    - a. **环节判断**: 从消息的 Payload 中解析出 `session_id`。判断该 `session_id` 是否为“一站到底”环节（例如 `one_stand_endurance_01`）。
        - 如果**不是**“一站到底”环节，则跳过后续校验，直接进入**动作2**的业务逻辑
        - 如果**是**“一站到底”环节，则继续执行下一步校验。
    - b. **复活机会校验**: 根据 Payload 中的 `user_id`，立即从 Redis 的 `event:{eventid}:player:{userId}` Hash 中查询该选手的 `revival_chances` 字段。
    - c. **执行拦截**:
        - 如果 `revival_chances` 的值**大于 0**，则校验通过，继续执行后续的所有标准处理流程（记录初始提交、执行判题逻辑等）。
        - 如果 `revival_chances` 的值**小于或等于 0**（或字段不存在），则证明该选手已被淘汰。服务**必须立即中止**对该条消息的处理，直接丢弃该次提交。服务不应向数据库写入任何记录，也不应执行任何计分或状态更新逻辑。同时，为了确保 EMQX Webhook 不会因错误响应而重试，服务仍需返回 `204 No Content`。服务可在内部记录一条警告级别的日志（例如：“已拦截来自已淘汰选手 `user_id` 的幽灵提交”）。
    - **动作2**: **逻辑处理服务**收到 HTTP 请求后，执行以下操作：
        - a. **记录初始提交**: 立即在 NocoDB `答题记录表`中插入一条记录，`grading_type` 字段标记为 `“自动判分”`，确保任何提交都第一时间被持久化。
        - b. **执行判题逻辑**: 从 Redis 缓存中获取正确答案和分值，与选手提交的 `submitted_answer` 进行比对，判断正误 (`is_correct`) 并计算得分 (`score`)。
        - c. **(新增) 处理复活机会**:
            
            此步骤为异步操作。为确保数据最终一致性，当对NocoDB的API调用失败时，服务不会丢弃该更新，而是会将更新任务封装后推入一个基于Redis List的**死信队列**。后台会有专用的处理器负责消费此队列，确保失败的更新操作最终能够被成功执行。
            
            - **i. 条件判断**: 检查 `is_correct` 是否为 `0` (错误)，并根据 `session_id` 判断当前是否为“一站到底”环节。
            - **ii. 更新机会**: 如果条件满足，使用 Redis 的 `HINCRBY` 命令对 `event:{eventid}:player:{userId}` 这个 Hash 中的 `revival_chances` 字段执行 `-1` 操作，原子性地减去一次复活机会。
            - **iii. 将缓存状态持久化 (NocoDB)**: 随后，**异步地**将 Redis 中最新的 `revival_chances` 数值**同步（或回写）**到 NocoDB 的`选手表`中
            - **iv. 广播状态更新 (MQTT)**: 立即读取该选手更新后的状态（剩余复活次数），并向 Topic **`{eventid}/result/player/status/update`** 发布一条消息，供大屏和选手端实时展示。
        - d. **更新排行榜缓存**: 在 Redis 中为该选手实时更新分数（例如，使用 `ZINCRBY` 命令操作排行榜的 Sorted Set）。
        - e. **更新答题记录**: 将 `答题记录表`中对应的记录更新为最终状态，写入 `is_correct` 和 `score` 字段。
1. **广播 (结果)**
    - **动作**: **逻辑处理服务**将包含所有细节的、完整的、已判分的最终结果，发布回 EMQX 的 `{eventid}/result/answer/grading/update` 主题。
    - **数据**:JSON
        
        `{"user_id": 1001, "session_id": "qa_mandatory_01", "question_id": 281, "question_number": 1, "submitted_answer": "D", "grading_type": "自动判分","is_correct":1,"score":10}`
        
2. **静默收集**
    - **动作**: **大屏端**和**中控端**订阅 `{eventid}/result/answer/grading/update` 主题。收到消息后，在前端的内部状态中记录下该选手的作答情况（答案、正误等），但界面上仅更新一个简单的“已提交”状态，不展示细节。
3. **人工触发展示**
    - **动作**: 在现场主持人发出信号后，**中控端**操作员点击【显示选手答案和正误】按钮，这会向 EMQX 的 `{eventid}/command/display/question/reveal_all` 主题发布一条指令。
4. **最终展示**
    - **动作**: **大屏端**监听到 `{eventid}/command/display/question/reveal_all` 指令，触发 UI 动画，将第 6 步中已预先收集好的所有选手的答案和用红绿色块区分的正误情况，同时、瞬间地展示出来。

### **4.2.2. 人工判题流程 (主观题，如手写填空题)**

此流程为人机协作设计，确保在需要人工判断时，数据流依然能回归到统一的逻辑核心，保证系统一致性。

1. **提交 (事件)**
    - **动作**: **选手端**将包含手写图片`data:image`数据发布到与自动判题完全相同的 EMQX 主题：`{eventid}/event/answer/submit`。
    - **数据**: `submitted_answer` 字段内容为data:image/png。JSON
        
        `{
          "user_id": 1001,
          "session_id": "qa_mandatory_01",
          "question_id": 282,
          "question_number": 2,
          "submitted_answer": "data:image/png"
        }`
        
2. **触发与初步处理**
    - **动作**: 同样由 EMQX Webhook 触发**逻辑处理服务**。
    - **处理**: 服务接收到数据后：
        - a. **查询题目信息**: 根据 Payload 中的 `question_id`，从 `题目表`中查询该题的 `question_type`。
        - b. **智能决策**:逻辑服务需要**根据 Payload 中的 `question_id`，从 Redis 缓存（如果赛前已预热）或直接从 NocoDB 的 `题目表` 中查询该题目的 `question_type` 字段**。
            - 如果 `question_type` 为“单选题”、“多选题”、“判断题”、“不定项选择题”，则立即执行**自动判题流程**。
            - 如果 `question_type` 为“填空题”、“简答题”，则启动人工判分流程：在 NocoDB `答题记录表`中插入记录，`grading_type` 字段标记为`“人工判分”`，并向 `{eventid}/event/adjudication/task/new` 主题发布一条包含待办任务详情的 **MQTT** 消息。
                - **Payload 设计说明:** 根据《Topic 设计》规范，此消息的 Payload 中**特意加入了 `user_name` 和 `question_prompt` 等冗余字段**。
                - Payload 示例:
                - `{
                "submission_id": 12345,
                "user_id": 1001,
                "user_name": "海事大学",
                "question_id": 282,
                "question_prompt": "请填写我国第一艘航空母舰的名称。",
                "submitted_answer": "data:image/png..."
                }`
                - **理由：** 这样设计是为了**优化人工裁判终端的性能和开发效率**，使其前端无需在接到任务后再去额外请求选手信息和题目信息，可以直接展示，提升响应速度。
3. **逐一展示与现场评判**
    - **动作**: 根据现场节奏，**中控端**操作员通过点击“上一位”、“下一位”或直接跳转，发布 `{eventid}/command/display/handwriting/prev`、`{eventid}/command/display/handwriting/next` 或 `{eventid}/command/display/handwriting/jump` 等指令。
    - **响应**: **大屏端**收到指令后，轮流展示每个选手的手写答案，供现场主持人和评委进行口头评判。
4. **人工录入判分结果**
    - **动作**: “记分组”人员在一个简单的内部 Web 应用——“人工裁判终端”上，实时看到所有待审核的答案。
    - **录入**: 听取现场评判结果后，记分组人员在终端上为对应的答案点击 [✔ 正确] 或 [❌ 错误] 按钮。
5. **提交判分结果**
    - **动作**: “人工裁判终端”的按钮点击后，会向**逻辑处理服务**的一个专用 HTTP API（例如 `POST /api/v1/grade/manual`）发起请求，提交与 `答题记录表` 字段匹配的判分结果。
    - **数据**: 请求的 Payload 中需包含`答题记录表`的唯一ID (`id`)、判分结果 (`is_correct`) 和最终得分 (`score`)。**其详细的接口定义和数据格式，请参见本文档【7.3. 接口详述 - 人工提交判分结果】部分。**
6. **最终处理与闭环**
    - **动作**: **逻辑处理服务**收到人工判分结果后，执行与自动判题流程几乎完全相同的收尾工作：
        - a. **更新记录**: 在 NocoDB `答题记录表`中更新对应记录的 `is_correct` 和 `score`。
        - b. **更新排行榜缓存**: 在 Redis 中为选手增加分数。
        - c. **广播结果**: 将这份最终被人工确认的完整结果，发布到与自动判题相同的 `{eventid}/result/answer/grading/update` 主题。
7. **实时反馈**
    - **动作**: **大屏端**订阅并监听到 `{eventid}/result/answer/grading/update` 的消息后，可以在当前展示的手写图片上，实时叠加一个“✔”或“❌”的视觉特效，完成现场的即时反馈。无论是自动还是人工判分，**最终都必须通过此 Topic 发布结果**。这使得下游（如大屏）可以订阅单一 Topic 来接收所有题目的判分反馈，大大简化前端逻辑。

### **4.2.3. 单题仲裁成功，作废该题所有作答数据 (最终一致性版)**

此流程旨在通过一个两阶段提交和后台补偿机制，精确且可靠地将一道题目的影响从所有选手中移除。

- **Topic 结构**: `{eventid}/command/quiz/question/invalidate`
- **发起方**: 中控端
- **Payload 示例**: `{ "question_id": 126, "reason": "C选项描述与事实不符，仲裁成功" }`

**逻辑处理服务 (Logic Handler Service) 的处理流程**:

1. **接收指令与日志记录**：服务订阅并接收指令，并将操作完整存入 `日志记录表`。
2. **阶段一：标记为“处理中”**
    - 服务根据 `question_id` (例如 `126`)，查询 NocoDB 的 `答题记录表`，找出所有 `status` 为 `有效` 的记录。
    - **执行原子更新**：调用 NocoDB 的批量更新接口，将所有这些记录的 `status` 字段**变更为 `作废中`**。
    - **风险控制**：如果此步数据库操作失败，流程终止并向上游（中控端）报告错误，系统状态未发生任何改变。
3. **阶段二：执行核心冲销与最终确认**
    - 在阶段一成功后，服务开始遍历这些已被标记为 `作废中` 的记录。
    - **a. 实时冲销 Redis 分数**：对每一条记录，获取其 `user_id` 和 `score`，使用 Redis 的 `ZINCRBY` 命令在排行榜中为对应用户减去分数。
    - **b. 更新 NocoDB 最终状态**：在所有记录的 Redis 分数都成功冲销后，再次调用 NocoDB 批量更新接口，将这些记录的 `status` 从 `作废中` **变更为 `作废`**。
4. **广播结果**：
    - 在 `status` 被更新为最终的 `作废` 状态后，服务从 Redis 获取最新排行榜，向 `{eventid}/result/rank/all/update` Topic 发布。
    - 同时，向 `{eventid}/result/system/notification/show` 发布系统通知，告知操作完成。
5. **补偿与和解机制 (后台任务)**
    1. **实现方式与并发控制:**
        - 在NestJS框架内，使用官方的Schedule模块 (`@nestjs/schedule`) 实现该定时任务（例如，`@Cron('*/5 * * * *')`，每5分钟执行一次）。
        - 为防止服务水平扩展后多个实例同时执行该任务，必须引入**分布式锁**。任务开始时，应尝试在Redis中使用`SET key value NX PX timeout`命令设置一个锁。只有成功设置锁的实例才能继续执行，其他实例则直接跳过本次执行。
    2. **增强的执行逻辑:**
        - 在获取到锁之后，任务的执行体应被一个完整的`try...catch...finally`块包裹。
        - `try`块中执行核心的补偿逻辑：查询所有“`作废中`”的记录，并逐一冲销Redis分数、更新NocoDB最终状态。
        - `catch`块中必须详细记录任何执行失败的错误，并**主动触发告警**，例如，向一个专用的内部监控Topic发布消息，或调用外部告警服务的Webhook。
        - `finally`块中必须确保释放Redis分布式锁，以便其他实例在下个周期可以接管。
    3. **监控:**
        - 该任务的**最后执行时间**、**执行结果（成功/失败）以及本次处理的悬挂记录数**应作为关键指标暴露给监控系统。当任务连续多次失败或长时间未成功执行时，应触发高级别告警。

### **4.2.4. 环节题包切换（最终一致性版）**

此流程用于处理因重大问题需要废弃整个环节的正式题包，并切换到备用题包的紧急情况。该操作影响范围广，涉及批量数据修改，因此必须采用最终一致性方案，以确保数据在跨系统（NocoDB 与 Redis）操作中的绝对可靠。

- **Topic 结构**: `{eventid}/command/quiz/pack/switch`
- **发起方**: 中控端
- **Payload 示例**:

```json
{
  "session_id": "qa_mandatory_01",
  "target_pack_id": 2,
  "reason": "正式题包多道题目出现争议，切换至备用题包"
}
```

**逻辑处理服务 (Logic Handler Service) 的处理流程**:

1. **接收指令与日志记录**：服务订阅并接收到 `{eventid}/command/quiz/pack/switch` 指令。服务将收到的 Topic 和 Payload 完整存入 NocoDB 的 `日志记录表`，确保所有高风险操作均可审计。
2. **阶段一：标记为“处理中”**
    - 服务根据 Payload 中的 `session_id` (例如 `qa_mandatory_01`)，查询 NocoDB 的 `答题记录表`，找出所有 `session_id` 匹配且 `status` 为 `有效` 的记录。
    - **执行原子更新**：服务调用 NocoDB 的批量更新接口，将所有这些记录的 `status` 字段**变更为 `题包作废中`**。
        - **最佳实践**: 按照文档建议，应收集所有待更新记录的 `Id`，然后**调用一次 `PATCH` 接口**完成批量更新，以提升性能。
    - **风险控制**：如果此步数据库操作失败，流程将终止并记录错误，系统核心数据未发生任何改变。
3. **阶段二：执行核心冲销与系统状态切换**
    - 在阶段一成功后，服务开始处理这些已被标记为 `题包作废中` 的记录。
    - **a. 实时冲销 Redis 分数**：遍历每一条记录，获取其 `user_id` 和 `score`，使用 Redis 的 `ZINCRBY` 命令在总排行榜 (`event:{eventid}:leaderboard`) 中为对应的 `user_id` 减去相应的分数。
    - **b. 更新系统核心状态 (Redis)**：这是**至关重要的一步**。在完成所有分数冲销后，服务必须更新 Redis 中与该环节相关的核心状态，使其指向新的题包：
        - **更新题包指针**：使用 `HSET` 命令更新赛事状态哈希表，例如：`HSET event:{eventid}:state pack_id_for_qa_mandatory_01 2`。
        - **清理题目缓存**：使用 `DEL` 命令清除可能存在的旧题包的题目缓存（如 `event:{eventid}:questions`），以防数据污染。
        - **预热新题包数据**：立即从 NocoDB `题目表` 中，查询 `session_id` 和新的 `question_pack_id` (例如 `2`) 对应的所有题目，并将其关键信息预加载到 Redis 缓存中。
4. **阶段三：更新最终状态**
    - 在 Redis 的所有操作（分数冲销、状态切换、缓存预热）都成功完成后，服务再次调用 NocoDB 批量更新接口，将这些记录的 `status` 从 `题包作废中` **变更为 `题包作废`**。
5. **广播结果，同步各端状态**
    - 在所有数据操作和状态变更完成后，服务会向客户端广播一系列结果消息，以同步各端状态：
    - **a. 更新排行榜**：从 Redis 重新获取完整的排行榜数据，向 `{eventid}/result/rank/all/update` Topic 发布最新的排行榜。
    - **b. 发送切换成功回执**：向 `{eventid}/result/quiz/pack/switched` 发布一条消息，明确告知中控端及其他客户端（特别是选手端）后台切换已完成，并指明当前激活的题包 ID。此回执是触发客户端清空并重新拉取题目的关键信号。
    
    ```json
    {
      "session_id": "qa_mandatory_01",
      "active_pack_id": 2
    }
    ```
    
    - **c. 发送系统通知**：向 `{eventid}/result/system/notification/show` Topic 发布消息，以便中控台和大屏能显示“‘有问必答’环节已切换至备用题包，请准备作答”之类的提示。
6. **后续流程**
    - 以上流程完成后，逻辑处理服务针对此次切换的工作即结束。它将等待中控端发起新的指令，例如，中控员在收到切换成功的回执和通知后，点击【出题】或【第1题】，发送 `{eventid}/command/quiz/question/switch` 指令。届时，逻辑服务会因其在第3步中更新的 Redis 状态，自动从新的备用题包中获取并下发题目。

### 4.3. 赛事与答题流程控制 (Quiz Flow & Control)

- **输入 (Topics):**
    - `{eventid}/command/system/player/refresh`
    - `{eventid}/command/system/screen/refresh`
    - `{eventid}/command/system/judge/refresh`
    - `{eventid}/command/system/host/refresh`
    - `{eventid}/command/system/all/go_home`
    - `system/bootstrap/assign_event`
- **处理逻辑:**
    - 对于 `refresh`、`go_home` 等主要由前端直接消费的指令，服务本身**无需执行核心业务逻辑**。但是，服务**仍应订阅这些 Topic**，并将接收到的原始消息（Topic 和 Payload）完整地存入 **NocoDB 的 `日志记录表`**，以形成一份完整的系统操作审计日志。
        - **理由**: 确保了系统行为的完全可追溯性，在出现意外情况时，可以复盘所有指令的下发顺序。
    - 对于 `assign_event`，服务需从 NocoDB 加载指定 `eventId` 的赛事基础信息（如队伍、题库、规则）到 Redis 中进行预热。体化预热内容：
        - 1. **赛事状态**: 创建 `event:{eventid}:state` Hash。
        - **2.选手信息**: 将`选手表`数据加载到`event:{eventid}:player:{userId}` Hash中。**此步骤必须从`选手表`中读取每位选手的`revival_chances`（复活机会）字段，并将其作为该选手在后续环节中的初始生命值。若在`选手表`中该选手的此字段为空或不存在，逻辑服务应默认其值为0，以做容错处理。**
            - **示例命令**: `HSET event:{eventid}:player:1001 revival_chances 1`
        - 3. **题目信息**: 将 `题目表` 的 `question_id`, `correct_answer`, `points`, `question_type` 等关键判分信息加载到 Redis 的 Hash (`event:{eventid}:questions`) 中。
        - 4. **环节配置**: 将 `环节表` 中的 `session_config` (如PK赛时长) 加载到 Redis。
        
        **选手初始状态校验逻辑**
        在完成上述所有数据的预热后，为了确保后续环节（尤其是一站到底）的顺利进行，服务**必须**执行一次选手初始状态的完整性校验：
        
        - **校验规则**：服务需遍历所有已加载到 Redis 的参赛选手，检查其 `revival_chances` 字段。
        - **触发条件**：如果发现任何一名选手的 `revival_chances` 值为 `null` 或 `≤ 0`，则必须触发警告。
        - **警告操作**：服务必须向 `{eventid}/result/system/notification/show` 主题发布一条 `level` 为 `warning` 的结构化消息。该消息的 `Payload` 中应包含明确的警告标题、提示信息以及具体存在配置问题的选手列表，以便中控员能快速定位并解决问题。此校验是保障赛事流程健壮性的关键一环。
- **输出:**
    - 加载赛事成功后，必须发布一条内容详尽的结果消息到 **`{eventid}/result/system/loaded`**。该消息的 Payload 应严格遵循《Topic 设计》的规范，为前端提供明确的加载成功确认和核心数据摘要。
    - Payload 示例:
    - `{
    "event_id": "pwfhg8yj7eb4rh2",
    "event_name": "2025年全国大学生国家安全知识竞赛",
    "status": "ready",
    "timestamp": "2025-07-23T12:30:00.123Z",
    "preloaded_items": {
    "players": 12,
    "questions": 85,
    "sessions": 5
    }
    }`
    - **理由：** 这样不仅能告知前端“服务已就绪”，还能让前端通过 `event_name` 确认加载了正确的赛事，并通过 `preloaded_items` 中的统计数据对数据完整性进行快速校验。

### **4.4. 核心指令与事件处理 (Core Command & Event Handling)**

- **输入 (Topics):**
    - `{eventid}/command/quiz/session/start`
    - `{eventid}/command/quiz/question/switch`
    - `{eventid}/command/quiz/question/next`
    - `{eventid}/command/quiz/question/prev`
    - `{eventid}/command/quiz/question/jump`
    - `{eventid}/command/quiz/answer/submit_now`
    - `{eventid}/event/answer/submit`
    - `{eventid}/event/judge/submit`
- **处理逻辑:**
    - **环节/题目切换:**
        - 收到 `session/start` 后，主要更新 Redis 的赛事状态缓存表 ( `event:{eventid}:state`) 中的`current_session`字段，以标记当前当前的初始状态。
        - **切换题目 ( `question/...`)** :
            1. **更新内部状态**: 服务收到包含`question_id`的指令后，立即更新 Redis 状态哈希表 ( `event:{eventid}:state`) 中的`current_question_id`字段。
            2. **查询单个问题题目详情**：为了获取用于接口显示的**单个**信息，服务**必须使用指令中指定的`question_id`，向NocoDB的`题目表`发起一次精确查询**。此查询的目标是获取**该特定**的所有字段，例如题干( `prompt`)、选项( `options`)等。
            3. **广播单个问题**：服务将上一步获取到**这道题**的完整数据，整理成符合《主题设计》规范的Payload，然后发布到`{eventid}/result/quiz/question/update`主题。
    - **选手提交答案 (`event/answer/submit`):** 该事件的处理遵循 `4.2.1` 和 `4.2.2` 节定义的 **自动或人工判题流程**。判题逻辑会高速访问已前置在Redis 中的答案和分值信息( `event:{eventid}:questions`)。
    - **评委提交评分 (`event/judge/submit`):**
        1. **记录日志:** 将评分数据写入 NocoDB 的 `评委评分表`。
        2. **暂存分数:** 当收到 `{eventid}/event/judge/submit` 事件时，服务应将评分数据存入 Redis 的一个专用数据结构中，例如一个 Hash，键为 `event:{eventid}:judging:{session_id}:{user_id}`，字段为 `judge_id`，值为 `judge_score`。
        3. **计算最终成绩**：服务需订阅并处理`{eventid}/command/judge/score/calculate`指令。收到该指令后，才从 Redis 中特定取出选手的所有评委暂存分，执行计算，然后将最终得分计入选手总分并更新排行榜。
- **输出 (Topics):**
    - 题目切换后，发布 `{eventid}/result/quiz/question/update`，Payload中包含从**NocoDB**获取的**单道题目**的详细信息，提供各端展示。
    - 选手答案提交并评分后，发布 `{eventid}/result/rank/all/update`，Payload 为更新后的完整排行榜数据。

### **4.5. 大屏显示与交互 (Display & Interaction)**

- **输入 (Topics):**
    - `{eventid}/command/display/rank/show`
    - `{eventid}/command/display/content/show`
    - `{eventid}/command/display/question/show_answer`
    - 等其他 `display` 指令。
- **处理逻辑:**
    - 当逻辑服务因为其他事件（如选手得分）而需要更新排行榜时，它会**主动**发布排行榜数据。
    - **需要**订阅并处理 `{eventid}/command/display/content/show` 指令
        - 接收到指令，从 Payload 中获取 `info_id`。
        - 根据 `info_id` 查询 NocoDB 的 `赛事素材表`。
        - 将查询到的 `title`, `content`, `attachment_url` 等信息，组装成 Payload。
        - 发布到 `{eventid}/result/display/content/update` Topic，供大屏端消费。
- **输出 (Topics):**
    - `{eventid}**/**result/rank/all/update`: **任何**导致分数变动的事件（自动答题、人工判分、评委打分计算、手动调分）的**最后一步**，都应该是重新从 Redis 读取完整的排行榜数据，并发布到此 Topic。
        - 从 Redis 的排行榜 Sorted Set 中获取所有选手的 `user_id` 和 `final_score`
        - 遍历每个 `user_id`，从 Redis Hash 或 NocoDB 中获取其 `user_name` 和详细的 `score_details`
        - 将这些信息组装成一个符合 `Topic 设计` 规范的、包含 `timestamp` 和 `leaderboard` 数组的完整 JSON 对象，然后发布。
        - 在组装 `leaderboard` 数组时，其中每个选手的 `score_details` JSON 对象，**必须是通过聚合该选手在 `答题记录表` 和 `评委评分表` 中的所有有效记录，并结合手动调整项（adjustments）来动态生成的**。这个生成逻辑必须与未来归档数据到 `最终得分表` 的逻辑保持严格一致。
            - **理由**: 明确了 `score_details` 这一复杂对象的权威数据源和生成规则，确保了实时推送的排行榜和赛后归档的排行榜在数据构成上是完全相同的。
            - **实现建议**: 为确保逻辑的绝对一致，逻辑处理服务内部应实现一个可复用的 `generateScoreDetails(userId)` 函数。无论是实时向 `{eventid}/result/rank/all/update` 推送排行榜，还是在赛事结束后归档数据到 `最终得分表`，都必须调用此同一个函数来生成 `score_details` JSON 对象。这能从代码层面确保数据结构和计算逻辑在任何时候都是统一的。
    - 对于客户端（尤其是大屏）首次加载排行榜的场景，可以采用“**HTTP拉取 + MQTT推送**”的混合模式，以优化体验和减少不必要的网络流量。
        - **首次加载**：客户端启动时，通过一个 HTTP GET 请求（如 `GET /api/v1/events/{eventId}/leaderboard`）从逻辑服务拉取一次完整的排行榜数据。
        - **后续更新**：客户端随后订阅 `{eventid}/result/rank/all/update` Topic，只接收增量更新。
        - **理由**：这样可以避免客户端在连接上的瞬间，收到大量可能在它离线期间累积的 MQTT 消息，并能更快地展示初始状态。
        - 逻辑处理服务在生成排行榜数据时，其构建 `score_details` 对象的逻辑，应该与最终存入 `最终得分表` 的JSON结构**严格保持一致**。这能确保无论是实时的 MQTT 推送，还是赛后从数据库查询，获取到的得分详情格式都是统一的，简化了所有消费端的处理逻辑。

### **4.6. 特定环节控制 (Specific Session Control)**

- **输入 (Topics):**
    - **安全公开课” / 评委打分环节:**
        - `{eventid}/command/judge/entry/start`
        - `{eventid}/command/judge/entry/end`
        - `{eventid}/event/judge/submit`
        - `{eventid}/command/judge/score/calculate`
    - **“终极PK”环节:**
        - `{eventid}/command/pk/phase/switch`
        - `{eventid}/command/pk/turn/switch`
        - `{eventid}/command/timer/global/control`
        - `{eventid}/command/timer/pro_team/control`
        - `{eventid}/command/timer/con_team/control`
    - **“争分夺秒”环节:**
        - `{eventid}/command/timer/race/start`
        - `{eventid}/command/timer/race/reset`
- **处理逻辑:**
    - **评委评分流程 (“安全公开课”环节):**
        1. **开启/关闭打分通道**: 
            - **开启**: 中控端发送 `{eventid}/command/judge/entry/start}` 指令，其 Payload 必须包含 `{ "user_id": 1001 }`。逻辑服务收到后，会将该 `user_id` 记录到 Redis 的赛事状态中（例如 `event:{eventid}:state` 里的 `judging_user_id` 字段），标记“当前正在为该选手评分”。评委端也基于此 `user_id` 展示选手信息。
            - **关闭**: 中控端发送不含 Payload 的 `{eventid}/command/judge/entry/end}` 指令。评委端收到后关闭评分界面。逻辑服务收到后则清除 Redis 中的 `judging_user_id` 状态，表示当前没有正在进行的评分活动。
        2.  **接收并处理评委个人评分 (支持修改)**：逻辑服务在收到评委端发来的 `{eventid}/event/judge/submit` 事件后，必须执行具备**幂等性**的 `UPSERT`（更新或插入）逻辑：
            - a. **查询校验**：服务需根据荷载中的 `judge_id`, `user_id`, `session_id`，检查 `评委评分表` (或 Redis 缓存) 中是否已存在该评委对该选手的评分记录。
            - b.查询代号：
                - 1.  根据荷载中的 `judge_id`，从**评委表 (Judge Table)** 中查询出对应的 `judge_name`（即“评委 1”等匿名代号）。
                - 2.  将 `judge_name` **添加**到即将广播的 `{eventid}/result/judge/score/submitted` 消息荷载中。
            - c. **执行 `UPSERT`**：
                - - **若记录不存在**，则执行**插入 (INSERT)** 操作，将评分数据写入 NocoDB 的 `评委评分表` 并同步至 Redis 暂存区。
                - - **若记录已存在**，则执行**更新 (UPDATE)** 操作，用新的 `judge_score` 覆盖旧的评分。此逻辑确保评委在打分通道关闭前可以自由修正自己的分数。
            - d. **实时广播**：无论插入还是更新，操作成功后，都立即向 `{eventid}/result/judge/score/submitted` Topic 广播包含最新分数的实时消息，用于大屏的即时展示。
        3. **计算并广播最终赋分**: 当收到中控端发来的 `{eventid}/command/judge/score/calculate` 指令时，服务执行以下操作：
            - a. **确定计算目标**: 服务**必须**从 Redis 的赛事状态（例如 `event:{eventid}:state` 的 `judging_user_id` 字段）中获取当前正在评分的选手ID。**服务应忽略指令荷载中任何可能存在的 `user_id` 字段**，以确保计算目标的唯一性和准确性。
            - b.前置校验与交互确认:
                - i. 校验数量: 从缓存中读取评委总数M，并从Redis中查询已收到的评分数N。
                - ii. 判断分支:如果 N < M 且指令Payload中不含 "force": true" 标记，则中止计算，并立即向 {eventid}/result/system/notification/show 主题发布一条包含明确警告信息的结构化消息，要求前端进行交互式确认。
                - 服务本次处理结束。如果 N >= M 或指令Payload中包含 "force": true" 标记，则继续执行后续计算。
            - c. 执行计算逻辑: 从 Redis 中获取所有评委对指定选手的暂存评分，执行计算逻辑（如去掉最高分和最低分后求平均值），得到最终赋分
            - d. **执行两次排行榜更新**：
                - **更新总排行榜**：使用 `ZINCRBY` 命令将最终赋分累加到全局总排行榜 (`event:{eventid}:leaderboard`) 中。
                - **更新环节排行榜**：使用 `ZINCRBY` 命令将最终赋分写入本环节独立的排行榜 (`event:{eventid}:leaderboard:judge_session`)。
            - e. **执行三次结果广播**：
                - 向 `{eventid}/result/judge/score/update` 发布包含详细计算结果的消息（维持现有逻辑）。
                - 向 `{eventid}/result/rank/all/update` 发布更新后的**完整全局排行榜**（维持现有逻辑）。
                - 向新增的 **`{eventid}/result/rank/judge_session/update`** 发布更新后的**本环节独立排行榜**。
            
            **特别说明：前端排序指令**
            
            请注意，在本环节的设计中包含一个由中控端发起的指令：`{eventid}/command/display/rank/sort_now`。
            
            - **职责划分**：该指令的设计目标是**纯粹由前端应用（如大屏端）进行消费**，用于触发客户端的UI动画（如列表排序）。
            - **服务无感知**：**逻辑处理服务（Logic Handler Service）不需要、也不应该订阅或处理此`sort_now`指令**。这确保了后端服务专注于核心的业务逻辑（计分、状态管理），而将与表现层相关的控制权交还给前端。
    - 争分夺秒计时器处理逻辑
        - **接收开始指令**: 当服务收到 `{eventid}/command/timer/race/start` 指令时，它会从 Payload 中获取总时长 `duration`。
        - **记录精确开始时刻**: 服务会使用 `Date.now()` 记录一个精确的 `startTime` (开始时间戳)。
        - **启动周期性检查器 (Ticker)**: 服务会启动一个 `setInterval` (例如每 500ms)，此检查器的唯一职责是周期性地计算并广播状态。
        - **基于真实时间差计算**: 在每次检查器回调中，服务会通过 `Date.now() - startTime` 来计算真实流逝的时间，并从而得出精确的 `remaining_time`。
        - **广播权威状态**: 服务将包含 `status`, `duration`, 和 `remaining_time` 的完整状态对象，组装成 Payload，发布到 **`{eventid}/result/timer/race/update`** 主题。
        - **处理暂停/恢复/重置**: 相应地处理其他控制指令，更新内部状态（如 `status`）并立即广播最新的状态。
        - **计时结束**: 当计算出的 `remaining_time` 小于等于 0 时，服务将 `status` 更新为 `finished` 并做最后一次广播，然后停止周期性检查器。
    - 争分夺秒提交答案事件的处理逻辑
        - 当服务在“争分夺秒”环节收到 `{eventid}/event/answer/submit` 事件后：
        1. **执行判分逻辑**：高速判断正误。
        2. **更新专用缓存**：
            - 如果回答正确，使用 `ZINCRBY` 命令更新 **`event:{eventid}:leaderboard:sprint`** 中对应选手的得分。
            - 使用 `HINCRBY` 命令更新 **`event:{eventid}:progress:sprint`** 中对应选手的 `progress` (已答题数) 和 `correct_count` (答对题数)。
        3. **广播轻量化进度**：
            - 从 **`leaderboard:sprint`** 和 **`progress:sprint`** 中读取最新的实时数据。
            - 组装成上文定义的轻量化Payload。
            - 向新增的专用Topic **`{eventid}/result/rank/sprint/update`** 发布更新。
        4. **不触碰总榜**：在此阶段，**绝对不要**发布更新到 `{eventid}/result/rank/all/update`。
        
        当180秒计时结束，并收到中控端发来的`{eventid}/command/quiz/sprint/finalize_score`指令后：
        
        1. **计算最终得分**：计算每个选手的“剩余时间奖励分”。
        2. **合并到总分**：将“本环节答题得分”与“时间奖励分”相加，得到本环节最终得分。
        3. **更新总排行榜**：使用 `ZINCRBY` 命令，将计算出的**最终得分**一次性地、原子地加到**总排行榜** (`event:{eventid}:leaderboard`) 中。
        4. **广播最终总榜**：此时，才向 **`{eventid}/result/rank/all/update`** 发布包含所有选手、所有环节得分详情的、完整的最终排行榜数据。
    - **终极 PK计时器控制:**
        - **记录开始时刻**:
        当逻辑服务收到一个开始计时的指令时，例如 `"{eventid}/command/pk/phase/switch"` 或 `"{eventid}/command/timer/pro_team/control"` 附带 `{"status": "start"}`，它**不**应该只启动一个简单的`setInterval`。正确的做法是，在执行逻辑的瞬间，用 `Date.now()` 记录一个精确的 **`startTime` (开始时间戳)**，并从环节配置中获取总时长 **`duration` (例如90秒)**。
        - **启动周期性检查器（Ticker）**:
        启动一个 `setInterval`，它的间隔可以设置得较短，例如 `200ms` 或 `500ms`。**请注意：这个间隔只决定了状态更新的频率，而不是用来计算剩余时间的依据。** 频率越高，客户端收到的更新越及时，但也会略微增加网络开销。
        - **增加内部状态管理**：
            - 在逻辑服务内部，为PK环节的计时器增加一个内存变量，例如 `isReminderSentForCurrentPhase: boolean`。
            - 当收到 `pk/phase/switch` 指令，切换到一个新的辩论阶段时，**必须将此变量重置为 `false`**。
        - **基于真实时间差计算剩余时间**:
        在 `setInterval` 的每一次回调中，执行以下增强逻辑：
            - a. 获取当前时间戳 `currentTime`，计算出 `elapsedTime` 和 `remainingTime`（维持现有逻辑）。
            - b. 从环节配置中获取当前阶段的提醒阈值 `reminder_at`。
            - c. **新增核心判断逻辑**：TypeScript
                
                ```tsx
                let shouldRemind = false;
                if (remainingTime <= reminder_at && !this.isReminderSentForCurrentPhase) {
                    shouldRemind = true;
                    this.isReminderSentForCurrentPhase = true; // 将标记置为true，防止重复发送
                }
                ```
                
            - d. **在广播的 Payload 中加入新标记**：将 `shouldRemind` 的结果以 `is_reminding` 为键名，加入到即将广播的 Payload 中。
        - **广播权威状态**:
        将上一步计算出的 `remainingTime`（以及其他如 `active_team` 等状态），组装成一个完整的状态对象，然后广播到 `"{eventid}/result/timer/pk_state/update"`。
    - **切换PK阶段 (`{eventid}/command/pk/phase/switch`):** 这是“终极PK”环节流程控制的核心指令。
        - **1.接收精简指令**: 逻辑服务订阅并接收来自中控端的指令。为遵循“单一数据源”原则，该指令的Payload应只包含核心意图，即要切换到哪个阶段。
            - **Topic**: `{eventid}/command/pk/phase/switch`
            - **Payload示例**: `{"phase_id": "opening_statement"}`
            
            **2.查询权威配置**: 服务收到指令后，根据Payload中的 `phase_id`，从已经预加载到Redis的环节配置中（源数据为NocoDB `环节表` 的 `session_config` 字段），查询出该阶段的完整权威信息，如 `phase_name` (阶段名称), `duration` (总时长), 和 `reminder_at` (提醒阈值)。
            
            **3.更新并广播权威状态**: 服务将查询到的完整配置信息，连同重置后的计时状态，更新到Redis中。随后，立即将这个完整的、权威的PK状态，通过 `{eventid}/result/timer/pk_state/update` 主题向所有客户端（包括大屏、中控端等）进行广播。
            
            - **广播Topic**: `{eventid}/result/timer/pk_state/update`
            - **广播Payload示例 (包含完整阶段信息)**:JSON
            
            `{
              "global_status": "running",
              "active_team": "pro_team",
              "pro_team": {
                "remaining_time": 90
              },
              "con_team": {
                "remaining_time": 90
              },
              "current_phase": {
                "phase_id": "opening_statement",
                "phase_name": "观点陈述",
                "duration": 90,
                "remaining_time": 90,
                "reminder_at": 15
              }
            }`
            
            **4.客户端响应**: 所有前端应用都只订阅该 `result` 主题。收到消息后，各端根据Payload中的权威信息同步更新界面，从而确保所有设备显示的阶段名称和倒计时绝对一致。
            
            - 切换PK发言方`{eventid}/**pk/turn/switch**`: 收到指令后，更新 Redis 中记录的当前发言方状态 (`active_team`)。
    - **终极 PK冠军裁定与智能计分逻辑**
        - 当逻辑处理服务收到 `{eventid}/command/pk/result/calculate` 指令时，必须严格执行以下“冠军裁定逻辑”：
        1. **获取数据:**
            - **PK环节得分**: 从 Redis 中读取本次PK环节两队的最终投票得分，记为 `pro_team_pk_score` 和 `con_team_pk_score`。
            - **赛前总分**: 从 Redis 全局总排行榜 (`event:{eventid}:leaderboard`) 中，查询出两队在PK**开始前**的累积总分，记为 `pro_team_total_score` 和 `con_team_total_score`。
        2. **判断 PK 胜者:**
            - 比较 `pro_team_pk_score` 和 `con_team_pk_score`，确定谁是 `pk_winner` (胜者) 和 `pk_loser` (败者)。
            - *（预处理逻辑：需预设平分情况下的裁决规则，此处假定必有胜负。）*
        3. **执行智能加分算法:**
            - 获取胜者 (`winner_total_score`) 和败者 (`loser_total_score`) 的赛前总分。
            - **核心判断:**
                - 情况A (胜者总分已领先):
                    
                    如果 winner_total_score >= loser_total_score，则无需特殊处理。胜者将自然保持领先地位。
                    
                - 情况B (胜者总分落后，需要“反超”):
                    
                    如果 winner_total_score < loser_total_score，则必须执行以下操作以保证名次反超：
                    
                    - 计算分数差：
                        
                        score_diff = loser_total_score - winner_total_score。
                        
                    - 计算冠军奖励分：
                        
                        bonus_points = score_diff + 1。
                        
                        （这 "+1" 分是确保总分能够超越败者的关键所在）
                        
                    - **将 `bonus_points` 添加给胜者**，作为本次裁定的额外奖励。
        4. **计算并更新最终总分:**
            - **胜者最终总分** = `winner_total_score` + `pk_winner_pk_score` + (情况B中计算出的 `bonus_points`，若为情况A则此项为0)。
            - **败者最终总分** = `loser_total_score` + `pk_loser_pk_score`。
        5. **更新排行榜与广播:**
            - 使用 Redis 的 `ZADD` 命令，将计算出的两队**最终总分**一次性、覆盖性地更新到总排行榜 (`event:{eventid}:leaderboard`) 中。
            - 立即向 `{eventid}/result/rank/all/update` Topic 广播包含最终排名的、完整的、权威的排行榜数据。
    - 处理终极PK投票事件 (`{eventid}/event/pk/vote/submit`) - 幂等性与防刷机制
        - 为确保终极PK投票的绝对公正性，即“**一票制，投完即锁定，不允许改票**”，服务在处理此事件时必须执行严格的幂等性校验。此逻辑与“安全公开课”环节评委可修正评分的 `UPSERT` 逻辑完全不同。
        1. **接收事件**：服务订阅并接收 `{eventid}/event/pk/vote/submit` 事件。
        2. **原子校验**：服务使用 Redis 的 `SADD` 命令，尝试将消息中的 `voter_id` 添加到键为 `event:{eventid}:pk_voters` 的 Set 集合中。
        3. **分支处理**:
            - **若 `SADD` 返回1 (首次提交)**: 此为有效投票。服务将继续执行后续所有业务逻辑：在 NocoDB `投票记录表`中创建记录，更新 Redis 中的实时票数缓存，并向 `{eventid}/result/pk/vote_progress/update` 广播最新票数。
            - **若 `SADD` 返回0 (重复提交)**: 此为无效投票。服务**必须立即静默忽略**该消息，不执行任何数据库写入和分数更新操作，以实现“投完即锁定”的业务规则。
        4. 补充说明：
            - 当逻辑处理服务收到投票事件并累加分数时，它不应只累加总分，而应在 Redis 中按队伍和投票者类型（`voter_type`） 分别进行累加。例如，使用 Hash 结构存储 `pro_team_expert_votes`, `pro_team_public_votes` 等。在向 `{eventid}/result/pk/vote_progress/update}` 广播时，必须组装成包含 `total_、expert_` 和 `public_` 字段的完整新 Payload 结构。
            
            ```json
            {
              "pro_team": {
                "total_votes": 10,
                "total_score": 32,
                "expert_votes": 4,
                "expert_score": 20,
                "public_votes": 6,
                "public_score": 12
              },
              "con_team": {
                "total_votes": 8,
                "total_score": 28,
                "expert_votes": 5,
                "expert_score": 25,
                "public_votes": 3,
                "public_score": 3
              }
            }
            ```
            
    
    当逻辑处理服务收到投票事件并累加分数时，它不应只累加总分，而应在 Redis 中按队伍和投票者类型（voter_type） 分别进行累加。例如，使用 Hash 结构存储 pro_team_expert_votes, pro_team_public_votes 等。在向 {eventid}/result/pk/vote_progress/update} 广播时，必须组装成包含 total_、expert_ 和 public_ 字段的完整新 Payload 结构。
    
- **输出 (Topics):**
    - `{eventid}/result/judge/score/update`：发布评委对单个选手计算后的最终赋分详情。
    - `{eventid}/result/timer/pk_state/update`：这是由服务**内部的计时器**（如 `setInterval`）周期性发布的，包含完整的PK环节计时状态。
    - `{eventid}/result/rank/update`：在任何导致总分变动的事件（包括评委赋分）完成后，发布最新的全局排行榜。
    - `{eventid}/result/timer/race/update` 争分夺秒”环节计时器状态更新数据

### **4.7 配置热更新机制 (Configuration Hot Reload Mechanism)**

为了支持赛事实时配置的灵活性和应对紧急情况，服务必须实现配置的热更新功能。

- **输入 (Topic):**
    - `{eventid}/command/system/config/reload`
- **处理逻辑:**
    1. **接收指令**: 服务订阅并接收到 `{eventid}/command/system/config/reload` 指令。
    2. **解析目标**: 从指令的 Payload 中解析出需要重载配置的 `session_id`。
    3. **重新查询**: 服务立即向 NocoDB 的 `环节表` 发起一次精确查询，获取指定 `session_id` 的最新记录，特别是其 `session_config` 字段。
    4. **更新缓存**: 将从数据库获取到的最新配置信息，覆盖性地更新到 Redis 的相应缓存中。例如，更新 `event:{eventid}:state` 哈希表中与该环节相关的配置项。
    5. **记录日志**: 在 `日志记录表` 中记录此次配置重载操作，以便审计。
- **输出 (Topic):**
    - `{eventid}/result/system/notification/show`
    - 在成功更新缓存后，服务必须向此 Topic 发布一条 `level` 为 `success` 的通知消息，明确告知中控端“环节 X 的配置已成功重载”，形成操作闭环。

### **4.8 全局缓存刷新机制 (Global Cache Refresh Mechanism)**

此机制为系统提供了一个最终的、全局的数据同步手段，用于从数据源（NocoDB）完全重建指定赛事（`eventId`）的实时缓存（Redis）。

- **输入 (Topic):**
    - `{eventid}/command/system/cache/refresh_all`
- **处理逻辑 (组合流程):**
服务收到此指令后，将严格按照以下顺序执行一个组合式的、幂等的操作流程：
    1. **记录操作日志**: 在 `日志记录表` 中记录此次高风险操作，以便审计。
    2. **清空并重载基础数据缓存**:
        - **清空**: 服务首先会删除 Redis 中与该 `eventId` 相关的所有**源于 NocoDB 的基础数据缓存**，包括但不限于：
            - 所有选手信息 (`event:{eventid}:player:*`)
            - 题目信息 (`event:{eventid}:questions`)
            - 环节配置 (`event:{eventid}:state` 中的相关字段)
        - **重载**: 随后，服务将**复用 `system/bootstrap/assign_event` 指令的核心逻辑**，重新从 NocoDB 的 `选手表`、`题目表`、`环节表` 中查询最新数据，并将其预热到 Redis 缓存中。
    3. **清空并重算衍生数据缓存**:
        - **清空**: 删除 Redis 中所有**通过计算得出的衍生数据缓存**，最主要的是总排行榜 (`event:{eventid}:leaderboard`) 以及各环节的独立排行榜（如 `leaderboard:sprint`, `leaderboard:judge_session` 等）。
        - **重算**: 随后，服务将**复用 `POST /api/v1/events/{eventId}/scores/recalculate` 接口的核心逻辑**。它会遍历 NocoDB `答题记录表` 和 `评委评分表` 中的所有有效记录，重新聚合计算出每个选手的最终总分，并重建整个排行榜缓存。
    4. **广播最终结果**:
        - 在所有缓存重建和分数重算完成后，服务会向 `{eventid}/result/rank/all/update` 发布一次全新的、完整的排行榜数据。
        - 同时，向 `{eventid}/result/system/notification/show` 发布一条成功通知，例如：“赛事ID: {eventId} 的全部缓存已刷新成功。”。

---

### **5. 数据模型 (Data Models)**

### **5.1. Redis (实时状态数据 - Hot Data)**

- **排行榜 (Sorted Set):**
    - `Key`: `event:{eventid}:leaderboard`
    - `Score`: 选手总分
    - `Member`: `userId`
- **争分夺秒排行榜 (Sorted Set):**
    - `Key`: `event:{eventid}:leaderboard:sprint`
    - `Score`: 选手在本环节的实时得分
    - `Member`: `userId`
- **争分夺秒进度 (Hash):**
    - `Key`: `event:{eventid}:progress:sprint`
    - `Field`: `userId`
    - `Value`: 一个JSON字符串，如 `{"progress": 15, "correct_count": 15}`
- **选手状态 (Hash):**
    - `Key`: `event:{eventid}:player:{userId}`
    - `Fields`: `name`, `logo`, `revival_chances`, `status` (e.g., `answering`, `answered`), `session_score_...`
- **赛事状态 (Hash):**
    - `Key`: `event:{eventid}:state`
    - `Fields`: `current_session`, `current_questionId`, `timer_status`, `timer_endtime`
- **题目信息 (Hash):**
    - `Key`:`event:{eventid}:questions`
    - `Fields`:`question_id`
    - `value`:`points`, `correct_answer`, `question_type`
- **评委评分暂存 (Hash):**
    - `Key`: `event:{eventid}:judging:{session_id}:{user_id}`
    - `Fields`: `judge_id`
    - `value`:`judge_score`
- **安全公开课环节排行榜 (Sorted Set):**
    - `Key`: `event:{eventid}:leaderboard:judge_session`
    - `Score`: 选手在本环节的最终赋分
    - `Member`: `userId`

### **5.2. NocoDB (持久化数据 - Cold Data)**

- **`日志记录表`:**
    - `Columns`: `timestamp`, `topic`, `payload (json)` , `status`
    - **用途:** 存储所有接收到的原始消息，用于审计和调试。
- **`最终得分表`:**
    - `Columns`: `user_id`, `user_name`, `final_rank`, `final_score`,`score_details (jsonb)`
    - **用途:** 赛事结束后，将 Redis 中的最终排行榜数据归档于此。
- **其他基础数据表:**  `题目表`, `赛事素材表` 等，在赛前配置，赛中主要为只读。

---

### **6. 非功能性需求 (Non-Functional Requirements)**

- **性能 (Performance):**
    - 对于常规消息（如提交答案），P99 响应延迟（从接收消息到发布结果）应低于 200ms。
- 数值精度 (Numerical Precision):
    - 鉴于《NocoDB 数据表设计》中的 `答题记录表` 和 `评委评分表` 的 score 字段均使用了 DECIMAL 类型，逻辑处理服务在进行所有分数相关的计算时（如累加、求平均值），必须规避 JavaScript/TypeScript 中标准的浮点数精度问题。推荐使用 `Decimal.js` 等高精度计算库，或将所有分值乘以 100 或 1000 转为整数进行计算，最后再转回小数进行存储和展示。
    理由: 提前规避常见的浮点数计算陷阱，确保赛事计分的准确无误，尤其是在涉及多位小数的评委打分场景
    - **理由**: 提前规避常见的浮点数计算陷阱，确保赛事计分的准确无误，尤其是在涉及多位小数的评委打分场景。
- **可靠性 (Reliability):**
    - 服务必须妥善处理与 Redis 和 NocoDB 的连接中断问题，并具备自动重连机制。
    - 对于处理失败的消息，应记录到错误日志，并根据策略进行重试或告警。
    - 所有后台定时任务（如数据和解任务）在设计上必须支持水平扩展，通过采用Redis分布式锁等机制确保在多实例环境下任务的唯一执行，避免重复处理。
    - 对于所有非关键路径的、由实时缓存（Redis）到持久化存储（NocoDB）的异步数据同步操作，必须采用**死信队列（DLQ）模式**。当同步失败时，将任务移入DLQ并由独立的、可靠的后台工作者进行重试，同时对DLQ的积压情况进行严格监控和告警，以确保数据的最终一致性。
- **可扩展性 (Scalability):**
    - 服务必须设计为**无状态**的。所有状态都存储在外部的 Redis 或 NocoDB 中。这使得服务可以水平扩展，通过运行多个实例来处理更高的并发量。
- **日志与监控 (Logging & Monitoring):**
    - **日志:**
    - `INFO` 级别：记录关键业务流程节点，如“开始处理环节 X”、“用户 Y 提交答案”。
    - `ERROR` 级别：记录所有处理异常、数据库连接失败等错误。
    - **监控:** 需暴露关键指标供监控系统采集，如消息处理速率、平均处理延迟、错误率等。
- 缓存策略 (Caching Strategy):
    - **事件驱动的缓存初始化**: 服务的缓存机制**不能**在自身启动时执行。相反，服务必须订阅并监听 `system/bootstrap/assign_event` 这个 Topic。当收到此消息后，服务执行以下操作：
        1. 从消息的 Payload 中解析出 `eventId` (即 NocoDB 的 `baseId`)。
        2. 在内存中为该 `eventId` 创建一个专用的表 ID 缓存实例。为支持多赛事并行，全局缓存结构应为一个以 `eventId` 为键的 Map，其值为该赛事的表名到表 ID 的映射。例如：`Map<EventID, Map<TableName, TableID>>`。
        3. 使用解析出的 `eventId` 调用 NocoDB 的 `GET /api/v2/meta/bases/{eventId}/tables` 接口，获取该赛事的所有表元数据。
        4. 将获取到的表名 (`table_name`) 和表 ID (`id`) 填充到对应 `eventId` 的缓存实例中。
    - **强制使用赛事级缓存**: 服务内部所有需要与 NocoDB 交互的地方，都必须首先从消息中获取 `eventId`，然后使用此 `eventId` 在全局缓存中找到对应的赛事缓存实例，并从中获取具体的表 ID。严禁在代码中硬编码任何表 ID。
    - **缓存刷新与失效机制**:
        - **被动刷新（出错时刷新）**: 当任何对 NocoDB 表的 API 调用返回 `404 Not Found` 状态码时，服务应视为对应 `eventId` 的缓存可能已失效。此时应自动为**该特定 `eventId`** 触发一次缓存刷新，然后对失败的请求进行重试。
        - **主动刷新（定时刷新）**: 服务可以为每个已加载的 `eventId` 维护一个刷新定时器（例如，每 30 分钟），周期性地主动同步其表元数据，以防止因意外情况导致的长期不一致。
        - **缓存清理**: 当赛事结束或中控端发出特定清理指令时，应将对应 `eventId` 的缓存从内存中移除，以释放资源。
- **数据一致性要求：**
    - 所有写入 NocoDB 的操作，其数据对象的字段名（Key）必须严格遵循《NocoDB 数据表设计》文档中定义的 `snake_case` 格式的**字段名 (Column Name)**。例如，向 `最终得分表` 归档数据时，使用的 JSON key 必须是 `user_id`, `final_rank`, `final_score`, `score_details` 等，确保数据模型的统一。
- **幂等性 (Idempotency):**
服务在设计上必须保证核心操作的幂等性，以应对由网络重试等原因可能导致的消息重复消费。
    - **示例1 (提交答案):** 在处理 `{eventid}/event/answer/submit` 时，应首先检查 NocoDB 的 `答题记录表` 中是否已存在相同 `user_id` 和 `question_id` 的记录。如果存在，则直接忽略本次提交，或返回已处理过的结果，而不是重复计算分数。
    - **示例2 (计算评分):** 处理 `{eventid}/command/judge/score/calculate` 指令时，需要设计状态锁或检查机制，确保同一环节、同一选手的评委平均分不会被重复计算并累加到总分中。
    **理由**: 幂等性是构建稳定可靠分布式系统的关键特性，可以有效防止数据因消息重发而出错
- 数据冗余原则
系统在多个数据传输对象（DTO/Payload）和持久化数据表（NocoDB）中采用了字段冗余的设计。
    - **示例1（优化前端开发）**: 向裁判端推送新的人工判分任务时（Topic: `{eventid}/event/adjudication/task/new`），Payload 中特意加入了 `user_name` 和 `question_prompt` 等冗余字段。
    - **示例2（优化查询性能）**: 在 `最终得分表` 中，除了 `user_id`，也冗余存储了 `user_name` 字段。
    
    **理由**:
    这是一种**有意为之的、以空间换时间**的设计策略。其核心目的是为了**优化消费端（如人工裁判终端、后台管理界面）的性能和开发效率**，使其在收到消息或查询数据时，能够直接获取到所需信息，避免了为获取关联信息（如选手名、题干）而发起额外的数据库查询，从而显著提升响应速度和用户体验。
    
    **遵循要求**:
    在开发过程中应遵循此设计。当冗余数据的源头（如 `选手表` 中的 `user_name`）发生变更时，需要考虑相应的缓存更新或数据同步机制，以确保数据的一致性。
    

---

### **7. 同步请求接口 (HTTP API)**

为支持系统间集成（如EMQX Webhook）和需要明确同步返回结果的低频管理操作，逻辑处理服务还需提供一套标准的HTTP RESTful API。

### **7.1. 基础架构**

- **实现方式：** 使用 NestJS 框架内置的 Controller 和路由功能实现。
- **API 前缀：** 所有 API 均以 `/api/v1` 作为前缀，以支持版本管理。
- **认证方式：** 所有对API的请求，必须在 HTTP Header 中包含有效的 `Authorization: Bearer <TOKEN>` 令牌进行认证，确保接口安全。
- **标准响应格式：**
    - **成功响应 (`200 OK`):**JSON
        
        `{
          "success": true,
          "data": { ... } // 具体的返回数据
        }`
        
    - **失败响应 (e.g., `400`, `404`, `500`):**JSON
        
        `{
          "success": false,
          "error": {
            "code": "ERROR_CODE", // 如 INVALID_PARAMETER
            "message": "详细的错误描述信息"
          }
        }`
        

### **7.2. 接口列表**

| **功能描述** | **HTTP 方法** | **Endpoint** | **主要用途** |
| --- | --- | --- | --- |
| **EMQX Webhook 接入点** | `POST` | `/api/v1/hooks/emqx` | 接收 EMQX Webhook 推送的 MQTT 消息数据，并将其分发到内部处理逻辑。 |
| **获取赛事实时状态** | `GET` | `/api/v1/events/{eventId}/state` | 供管理后台查询或调试，获取指定赛事的完整实时状态（从Redis读取）。 |
| **强制重算排行榜** | `POST` | `/api/v1/events/{eventId}/scores/recalculate` | 管理操作，用于修复数据错误，触发对整个赛事的得分和排名进行重新计算。 |
| **手动调整分数** | `POST` | `/api/v1/events/{eventId}/players/{userId}/adjust-score` | 管理操作，为特定选手手动增加或扣除分数。 |
| 人工提交判分结果 | `POST` | `/api/v1/grade/manual` | 接收人工裁判终端对主观题的判分结果 |
| 重载环节配置 | `POST` | `/api/v1/events/{eventId}/sessions/{sessionId}/reload-config` | 管理操作，手动触发服务重新从 NocoDB 加载指定环节的配置。 |
| 刷新赛事全部缓存 | `POST` | `/api/v1/events/{eventId}/cache/refresh` | 管理操作，触发对指定赛事所有缓存的清理、重载和重算。 |

### **7.3. 接口详述**

- **EMQX Webhook 接入点**
    - **Endpoint:** `POST /api/v1/hooks/emqx`
    - **Request Body:** EMQX推送的标准消息格式（通常是一个JSON数组）。JSON
        
        `[
          {
            "topic": "some_event_id/event/answer/submit",
            "payload": "{\"user_id\":\"1001\",\"question_id\":281,\"submitted_answer\":\"D\"}",
            "clientid": "player_client_123",
            "timestamp": 1721542109000
          }
        ]`
        
    - **处理逻辑：** 服务接收到请求后，立即返回 `204 No Content` 以告知EMQX已接收。随后，服务异步地遍历数组中的每条消息，并将其路由到与MQTT订阅模式完全相同的内部业务处理模块。该接口的核心职责是作为消息的HTTP入口，不应包含具体的业务逻辑。
    - **Success Response:** `204 No Content`
    - **注意**：服务在接收到请求后，需要对 `payload` 字段的值**执行一次 `JSON.parse()`** 才能获取到原始的客户端消息对象”。这可以有效避免潜在的解析错误
- **手动调整分数**
    - **Endpoint:** `POST /api/v1/events/{eventId}/players/{userId}/adjust-score`
    - **Request Body:**JSON
        
        `{
          "adjustment": -10,
          "reason": "技术犯规，扣除10分"
          "operator_id": "admin_user_01", // 从认证信息中获取
          "timestamp": "2025-07-25T10:00:00Z"
        }`
        
    - **处理逻辑：**
        1. 验证 `eventId` 和 `userId` 的有效性。
        2. 在 Redis 的排行榜 Sorted Set 中对该 `userId` 的分数执行 `ZINCRBY` 操作，值为 `adjustment`。
        3. 记录到**`最终得分表`**的 `score_details.adjustments` 数组中，包含操作人、调整值和原因。
        4. 触发一次排行榜更新事件，发布到 `{eventId}/result/rank/update`。
        5. 在`日志记录表`中创建一条记录，`topic` 字段可以记为 `http:post:/api/v1/events/{eventId}/players/{userId}/adjust-score`，`payload` 字段记录请求的 Body 内容。
    - **Success Response:** `200 OK`，`data` 中包含选手的新总分。
- 人工提交判分结果
    - **Endpoint:** `POST /api/v1/grade/manual`
    - **Request Body**: `{ "id": 12345, "is_correct": 1, "score": 10 }` (其中 `id` 为`答题记录表`的唯一ID，`is_correct` 和 `score` 为判分结果)。
    - **处理逻辑**: 服务收到请求后，用`id`在`答题记录表`中找到对应记录，更新其 `is_correct` 和 `score` 字段，并触发后续的排行榜更新和结果广播流程。
- 获取环节题包
    - **Endpoint:** `GET /api/v1/events/{eventId}/sessions/{sessionId}/questions`
    - **URL 参数**:
        - `{eventId}` (string, required): 赛事的唯一ID。
        - `{sessionId}` (string, required): 环节的唯一ID (例如: `sprint_180`)。
    - **处理逻辑**:
        1. 服务接收到请求后，从 URL 中解析出 `eventId` 和 `sessionId`。
        2. 从 Redis 的赛事状态哈希表 (`event:{eventId}:state`) 中，读取当前该环节 (`sessionId`) 绑定的题包ID (`question_pack_id`)。这是为了支持备用题包切换功能。
        3. 使用 `sessionId` 和 `question_pack_id` 作为查询条件，向 NocoDB 的 `题目表` 发起查询，获取所有符合条件的题目记录。
        4. 遍历查询结果，为每道题目**组装一个精简的、不包含答案和附件的** JSON 对象。
        5. 将所有题目对象组成的数组作为响应数据返回。
    - **成功响应 (`200 OK`)**:
        - Body 中返回一个 JSON 数组，其中每个对象代表一道题目。
        - **关键**: 响应载荷中**严禁包含 `correct_answer`、`points` 、**`attachment_url`**等任何判分依据和附件字段**，以确保赛事的公平性。
        
        ```json
        {
          "success": true,
          "data": [
            {
              "question_id": 401,
              "question_number": 1,
              "question_type": "单选题",
              "prompt": "以下哪个选项是国家安全的核心？",
              "options": [
                { "key": "A", "text": "经济安全" },
                { "key": "B", "text": "政治安全" },
                { "key": "C", "text": "军事安全" }
              ]
            },
            {
              "question_id": 402,
              "question_number": 2,
              "question_type": "判断题",
              "prompt": "维护国家安全是所有公民的义务。",
              "options": [
                { "key": "A", "text": "正确" },
                { "key": "B", "text": "错误" }
              ]
            }
            // ... more questions
          ]
        }
        ```
        
        **失败响应**:
        
        - 若 `eventId` 或 `sessionId` 无效，返回 `404 Not Found`。
- 强制重算排行榜
    - **Endpoint:** `POST /api/v1/events/{eventId}/scores/recalculate`
    - **Request Body:** `{}` (可为空)
    - **处理逻辑：**
    此接口用于在出现数据不一致等特殊情况时，从原始数据源强制重构整个赛事的实时排行榜。
        1. **记录操作日志**：在 `日志记录表` 中记录此次手动重算的操作，`topic` 字段可记为 `http:post:/api/v1/events/{eventId}/scores/recalculate`。
        2. **清空当前排行**：使用 Redis 的 `DEL` 命令删除当前的排行榜 Sorted Set (例如 `event:{eventid}:leaderboard`)。
        3. **遍历所有选手**：从 `选手表` 中获取所有选手的 `user_id`。
        4. **重新聚合得分**：对每一个 `user_id`，执行以下操作：
            - a. 初始化总分为 0。
            - b. 查询该用户在 `答题记录表` 中所有 `status` 为 `“有效”` 的记录，累加其 `score` 字段。
            - c. 查询该用户在 `评委评分表` 中的所有评分，根据环节规则（如去高低分求平均）计算出评委赋分并累加。
            - d. （可选）聚合手动调整分数：如果调整记录存储在特定位置（例如 `最终得分表` 的 `score_details.adjustments` 数组），则在此处一并计入。
            - e. 将上述得分相加，得到该选手的最终总分（`final_score`）。
        5. **重建排行榜缓存**：使用 Redis 的 `ZADD` 命令，将计算出的每个选手的 `final_score` 和 `user_id` 重新写入排行榜 Sorted Set。
        6. **广播最终结果**：完成所有选手的重算后，从 Redis 获取最新的排行榜数据，向 `{eventid}/result/rank/all/update` Topic 发布一次全新的、完整的排行榜数据。
    - **Success Response:** `200 OK`，`data` 中可以包含重算后的选手总数或简要成功信息。

---

### **8. 超出范围 (Out of Scope)**

- 本服务不负责任何前端 UI 的渲染。
- 本服务不负责用户身份认证（AuthN）和权限管理（AuthZ），假定这些由其他服务或 EMQX 的 ACL 规则处理。
- 本服务不直接管理 EMQX Broker 的配置。
- 本服务不负责题库、选手名单等基础数据的录入和管理界面（这由 NocoDB 提供）。

### **9.明确数据源**:

在处理 `event/answer/submit` 事件时，服务需要根据 `question_id` 从 Redis 缓存（或 NocoDB `题目表`）中查询 `question_type` 和 `points`，这是决定走自动判分还是人工判分流程的关键决策点。

在 `event/judge/submit` 的处理逻辑中，将评委提交的分数**暂存**到 Redis 的一个数据结构中（例如 Hash：`event:{eventid}:judgescores:{userId}`），直到收到 `command/judge/score/calculate` 指令才进行计算

### 10.错误处理

- **输出 (Topics):`{eventid}/result/system/error/notify`**
    - 当逻辑服务处理 `command` 或 `event` 失败时（例如，因 `question_id` 不存在而无法切换题目），除了在服务端记录日志，还应**主动向 `{eventid}/result/system/error/notify` Topic 发布一条结构化的错误消息**。
    - **Payload 示例 (遵循《Topic 设计》):**JSON
        
        `{
          "timestamp": "2025-07-23T12:30:00.123Z",
          "level": "error",
          "error_code": "RESOURCE_NOT_FOUND",
          "message": "指令执行失败：找不到指定的资源。",
          "details": {
            "source_topic": "{eventid}/command/quiz/question/switch",
            "source_payload": { "question_id": 999 },
            "suggestion": "请确认您操作的题目ID是否正确，或检查题库中是否存在该题目。"
          }
        }`
        
    - **理由：** 这能让中控台等管理端获得实时的、可操作的错误反馈（如直接在界面上弹出 `message` 和 `suggestion` 的内容），极大提升系统的可运维性。