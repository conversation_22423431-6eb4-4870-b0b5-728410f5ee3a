# 赛事执行手册

本手册旨在为开发人员提供一份完整的、端到端的赛事执行流程指南。它详细描述了从赛前准备到赛后归档的每一个步骤，并明确了每个步骤中涉及的角色、操作、系统交互、技术触发点以及预期的系统响应。

**核心交互流程:**

- **指令下发:** **中控端**是指令的唯一发起者，通过向特定 MQTT Topic (`.../command/...`) 发布消息来控制赛事进程。
- **事件上报:** **选手端**和**评委端**是事件的主要上报方，通过向特定 MQTT Topic (`.../event/...`) 发布消息来提交数据（如答案、评分）。
- **逻辑处理:** **逻辑处理服务 (Logic Handler Service)** 是系统的大脑，它订阅所有 `command` 和 `event` Topic。收到消息后，它会执行业务逻辑（计分、计时、流程控制），读写 NocoDB（持久化数据）和 Redis（实时缓存），并将处理结果通过 `.../result/...` Topic 广播出去。
- **结果展示:** **大屏端**、**选手端**、**中控端**等所有前端应用都订阅 `result` Topic，接收逻辑服务广播的权威数据，并据此更新界面显示。

**模板参考:**

- **时间点:** 预计的环节开始时间或相对时间。
- **环节/阶段:** 赛事的具体部分。
- **执行角色:** 操作的发起者。
- **具体动作/指令:** 在前端界面上的具体操作。
- **系统交互/技术触发点:** 该操作触发的后端技术事件，包含 MQTT Topic、Payload 示例和后端服务逻辑。
- **预期系统响应/结果:** 系统在接收到触发信号后应有的反应，包含发布到哪个结果 Topic、更新了哪些数据以及前端的最终表现。

---

## O、 赛前准备与系统初始化

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | 系统引导 | **中控员** | 在中控台选择本次赛事，点击【加载赛事】。 | **[指令]** 中控端向 `system/bootstrap/assign_event` 主题发布消息。
**Payload:** `{"eventid": "pwfhg8yj7eb4rh2"}` | **1. 逻辑服务响应:**   - 监听到 `assign_event` 指令，从 Payload 获取 `eventid`。
- 从 NocoDB 数据库中查询与该 `eventid` 相关的所有基础数据。 
- **数据预热到 Redis:**     
- **选手信息:** 从 NocoDB 的 `选手表` 中，加载每位选手的详细数据到 Redis Hash `event:{eventid}:player:{userId}`。**此步骤会特别读取并预热每位选手在该赛事中的初始复活机会（`revival_chances`字段），请在赛前务必于`选手表`中核对并为每位参赛选手配置好此数值。** 
- **题目信息:** 从`题目表`加载`question_id`, `correct_answer`, `points`, `question_type`到 Redis Hash `event:{eventid}:questions`。
- **环节配置:** 从`环节表`加载`session_config` (如PK赛各阶段时长) 到 Redis。
- **创建排行榜:** 初始化一个空的 Redis Sorted Set `event:{eventid}:leaderboard`。
**2. 结果广播:**   
- 预热完成后，逻辑服务向 `{eventid}/result/system/loaded` 主题发布消息。
**Payload:** `{"event_id": "...", "event_name": "...", "status": "ready", "preloaded_items": {"players": 12, "questions": 85, "sessions": 5}}`
**3. 前端响应:**   
- 中控台、大屏、选手端等收到 `loaded` 消息，确认赛事名称无误，并渲染初始界面（如欢迎页、选手列表）。
4**. 系统配置校验与警告：**
 - 在数据预热完成后，逻辑服务会自动校验所有选手的关键配置（如“一站到底”环节的复活机会）。 
- **情况 A (配置完整):** 无任何提示，流程继续。 
- **情况 B (配置不完整):** 如果检测到有选手的复活机会未配置或为0，逻辑服务会向 `{eventid}/result/system/notification/show` 主题发布警告。
**5. 前端响应:**  
- **正常情况：中控台、大屏等收到 `loaded` 消息，确认赛事名称无误，并渲染初始界面。
- 警告情况：中控台在渲染初始界面的同时，会弹出一个明确的警告提示框，内容为：“**检测到部分选手的'复活机会'未配置或为0，请立即检查赛事数据！**”，以提醒中控员在赛前修正问题。 |
|  2 | 设备检查 | **中控员** | 点击【全部刷新】或分别点击【刷新大屏】、【刷新选手端】。 | **[指令]** 中控端向以下一个或多个主题发布空载荷消息：`{eventid}/command/system/screen/refresh、
{eventid}/command/system/player/refresh、
{eventid}/command/system/judge/refresh` | **1. 逻辑服务响应:**   - 订阅这些主题，接收到消息后，将其完整存入 NocoDB `日志记录表`，用于操作审计。
**2. 前端响应:**   
- 各个前端应用监听到对应的 `refresh` 指令后，执行页面刷新或状态重置操作，确保所有设备处于最新就绪状态。 |

---

## 一、开幕式

**环节目标：** 在比赛正式开始前，依次向现场观众、领导和参赛队伍展示参赛队伍信息、介绍领导嘉宾，并由领导宣布比赛开始，营造仪式感。

**前置条件：** 所有用于开幕式展示的素材，如：各队伍的Logo、名称、口号，以及领导的姓名等，都已预先录入到 NocoDB 的 `赛事素材表` 中。

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | **队伍巡礼** | **中控员** | 主持人宣布开幕式开始。中控员点击【开始队伍巡礼】，或点击特定队伍的【展示】按钮。 | **[指令]** 中控端向 `{eventid}/command/display/content/show` 发布消息。
**Payload:** `{"info_id": 101, "info_type": "team_intro"}` (info_id 对应素材表中具体队伍的ID) | **1. 逻辑服务响应:** 
- 收到指令，根据 `info_id` 从 NocoDB `赛事素材表` 查询对应队伍的详细信息（如队名、Logo图片URL、口号等）。
- 向 `{eventid}/result/display/content/update` 发布包含该队伍信息的权威数据。
**2. 前端响应:**
- **大屏端**监听到 `content/update` 消息，播放入场动画，并居中显示当前队伍的队名和Logo。 |
| 2 | **循环展示** | **中控员** | 主持人介绍完当前队伍后，中控员点击【下一队】。 | **[指令]** 中控端再次向 `{eventid}/command/display/content/show` 发布消息。
**Payload:** `{"info_id": 102, "info_type": "team_intro"}` | **1. 系统响应:**
- 系统流程同上，大屏内容更新为下一支队伍的信息。
- 此流程循环执行，直到所有参赛队伍都展示完毕。 |
| 3 | **2介绍领导** | **中控员** | 主持人介绍出席的领导嘉宾。中控员点击【展示领导信息】。 | **[指令]** 中控端向 `{eventid}/command/display/content/show` 发布消息。
**Payload:** `{"info_id": 201, "info_type": "leader_intro"}` | **1. 逻辑服务响应:**
- 根据 `info_id` 从 `赛事素材表` 查询领导的姓名、职位等信息。
- 向 `{eventid}/result/display/content/update` 发布数据。
**2. 前端响应:**
- **大屏端**显示领导的姓名和职位介绍。 |
| 4 | **宣布开始** | **主持人 / 中控员** | 主持人邀请领导宣布比赛开始。领导宣布后，中控员点击【宣布比赛开始】。 | **[指令]** 中控端向 `{eventid}/command/display/content/show` 发布消息。
**Payload:** `{"info_type": "event_start_animation"}` | **1. 逻辑服务响应:**
- 逻辑服务可直接透传或根据 `info_type` 查找特定动画素材的URL。
- 向 `{eventid}/result/display/content/update` 发布指令。
**2. 前端响应:**
- **大屏端**播放一个炫酷的“比赛正式开始”的片头动画，随后自动切换到第一个比赛环节的待机界面。开幕式结束。 |

## 二、 有问必答环节

本环节整体分为“必答题”与“挑战题”两个阶段。系统将根据所选题目自身的 `question_type` 属性，自动进入“自动判分”（适用于选择题等客观题）或“人工判分”（适用于填空、手写等主观题）流程。

### 2.1 必答题阶段

本环节共8道题，每题10分，客观题限时5秒，主观题限时 10 秒，统一倒计时结束后自动提交。题目将面向所有选手，每位选手独立作答并计分。

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | 进入环节 | **主持人/中控员** | 主持人宣布环节开始。中控员点击【有问必答】，再点击【开始】。 | **[指令]** 中控端向 `{eventid}/command/quiz/session/start` 发布消息。
**Payload:** `{"session_id": "qa_mandatory_01", "session_name": "有问必答"}` | **1. 逻辑服务响应:**   
- 在 Redis 状态哈希表 (`event:{eventid}:state`) 中更新 `current_session` 为 `qa_mandatory_01`。
**2. 前端响应:**   
- 大屏、选手端等切换到“有问必答”环节的待机界面。 |
| 2 | 显示规则 | **中控员** | 点击【显示规则】。 | **[指令]** 中控端向 `{eventid}/command/display/content/show` 发布消息。
**Payload:** `{"info_id": 1, "info_type": "规则介绍"}` | **1. 逻辑服务响应:**   
- 收到指令，根据 `info_id` 从 NocoDB `赛事素材表` 查询规则详情。
- 向 `{eventid}/result/display/content/update` 发布包含标题和内容的权威数据。
**2. 前端响应:**   
- 大屏端监听到 `content/update` 消息，显示环节规则。 |
| 4 | 出第1题 | **中控员** | 点击【第 1 题】。 | **[指令]** 中控端向 `{eventid}/command/quiz/question/switch` 发布消息。
**Payload:** `{"question_id": 285, "question_number": 1}` | **1. 逻辑服务响应:**  
- 更新 Redis 中的 `current_question_id`。 
 - 从 NocoDB `题目表` 查询该题的完整信息 (题干、选项、`question_type`等)。
- 向 `{eventid}/result/quiz/question/update` 发布完整的题目数据。
**2. 前端响应:**  
- 所有选手端、大屏、中控台收到题目数据，并同步显示题目内容。选手端答题按钮激活并开始倒计时。 |
| 4 | 选手答题 | **选手** | 在平板上选择（或手写）答案，并**在本地设备上暂存**该选择，等待统一提交指令。 | **(无即时系统交互)**  
- 选手的作答数据（选择的选项或手写笔迹）仅保存在选手端设备的内存中，尚未向服务器提交。
**实现本地持久化存储**：选手在设备上做出选择或修改答案的瞬间，就应立即将答案写入设备的本地存储（如Local Storage或一个小型数据库），而不仅仅是保存在内存里。这样，即便应用重启，也能从本地恢复到之前的作答状态，等待提交指令。 | **1. 前端响应:**  
- 选手端界面实时反馈选手的选择（例如选项高亮），倒计时结束前可更改选项。 |
| 5 | 时间到/强制提交 | **中控员/系统** | 5秒/10 秒倒计时结束，或中控员点击【强制提交】。 | **[前端触发与重试机制]** 
- 收到`{eventid}/command/quiz/answer/submit_now` 指令后，**立即将在本地暂存的答案**采用**带重试机制的降级策略**上报。若首次上报失败（如网络波动），选手端将自动在后台进行**多次重试**，直到收到服务端的成功回执或重试超时。
- 为确保送达，上报的 MQTT 消息应使用 **QoS 2**的服务质量等级。
**[事件上报]** 选手端向 `{eventid}/event/answer/submit` 发布消息。
**Payload (客观题):** `{"user_id": 1001, ..., "submitted_answer": "B"}`
**Payload (主观题):** `{"user_id": 1002, ..., "submitted_answer": "data:image/..."}` 
**[Webhook触发]** EMQX 通过 Webhook 将消息同步请求到逻辑服务的 `POST /api/v1/hooks/emqx` 接口。
 | **1. 前端响应:**  
- 所有选手端的答题界面被锁定。  
**2. 逻辑服务根据 `question_type` 自动路由处理流程：**  
**情况A： (自动判题流程):**  
- a. 立即在 NocoDB `答题记录表` 插入一条记录，`grading_type` 标记为 `“自动判分”`，`status`为`有效`。
- b. 从 Redis 缓存 (`event:{eventid}:questions`) 中获取该题的 `correct_answer` 和 `points`
- c. 比对答案，计算出 `is_correct` 和 `score`。
- d. 使用 `ZINCRBY` 命令更新 Redis 排行榜 (`event:{eventid}:leaderboard`)。
- e. 更新 NocoDB `答题记录表` 中对应记录的 `is_correct` 和 `score`。
**情况 B：**(人工判题流程):
- a. 根据 `question_id` 从 Redis/NocoDB 查出 `question_type` 为“填空题”。
- b. 在 NocoDB `答题记录表` 插入记录，`grading_type` 标记为 `“人工判分”`，`status`为`有效`。
- c. 向 `{eventid}/event/adjudication/task/new` 主题发布一条**待办任务**消息。  
**Payload:** `{"submission_id": 12345, "user_name": "...", "question_prompt": "...", "submitted_answer": "data:..."}`(包含冗余字段以优化前端)
**3. 结果广播:**  
- **对于自动判题流程:** 逻辑服务向 `{eventid}/result/answer/grading/update` 发布已判分的最终结果。
- **对于人工判题流程:** 此阶段**不发布**最终结果。结果将在后续“人工录入”环节完成后，才向该 Topic 发布。  
**4. 前端状态更新:**  
- **对于自动判题流程:** 大屏和中控台订阅 `grading/update`，收到消息后在内部记录下该选手的作答情况，但界面仅显示“已提交”状态，等待统一公布。
- **对于人工判题流程:**      
- a. 提交后，大屏等界面对该选手**立即显示“已提交”状态**。      
- b. 系统进入等待状态，等待后续由中控员手动触发“现场展示”，以及“记分组人员”执行“人工录入”操作。      
- c. 只有在“人工录入”完成后，`grading/update` 消息才会被广播，大屏端收到后才能显示最终的评判反馈（如✔或❌特效）。
5**. 流程健壮性:**
- **超时处理:** 若在指定窗口期后逻辑服务仍未收到某选手的答案（即使客户端重试后），系统会将其标记为“异常”，现场工作人员上台检查设备状态和选手选择的答案，通报后台进行答案的提交，并且更换设备，确保整体流程不被阻塞。 |
| **6** | **公布正确答案** | **中控员** | 点击【公布本题答案】。 | **[指令]** 中控端向 `{eventid}/command/display/question/show_answer` 发布消息。 | **1. 逻辑服务响应:**  
- 收到指令，从 Redis/NocoDB 中获取当前题目的正确答案。  
- 向 `{eventid}/result/display/answer/update` 发布包含正确答案的数据。
**2. 前端响应:**  
- **大屏端监听到 `answer/update` 消息，在屏幕上清晰地显示出本题的标准答案。** 这为现场所有人员（包括评委）提供了评判依据。 |
| 7 | **公布作答详情** | **中控员** | 点击【显示选手答案和正误】。 | **[指令]** 中控端向 `{eventid}/command/display/question/reveal_all` 发布空载荷消息。 | **1. 前端静默收集 (对两种情况通用):**  
- 大屏和中控台订阅 `grading/update` (自动判分) ，内部记录作答情况，界面仅显示“已提交”，不透露结果。
**2. 前端响应:**  
- 大屏监听到指令，开始展示所有选手的作答详情。    
- **对于自动判题:** 直接显示每位选手的答案及系统自动判定的“正确”或“错误”标记。    
- **对于人工判题:** **仅显示每位选手提交的手写答案图片**，不附加任何对错标识，等待现场评判。
 |
| 8 | 现场展示
**(仅当题目为人工判题)** | **中控员** | 点击【放大填空题手写答案】。 | **[指令]** 中控端向 `{eventid}/command/display/handwriting/show` 发布消息。 | **1. 前端响应:**   
- 大屏端展示第一个提交答案的选手的手写图片，供现场主持人和评委评判。 |
| 9 | 现场评判
**(仅当题目为人工判题)** | **主持人/评委** | 口头宣布答案正确或错误。 | (无系统交互) | (无系统交互) |
| 10 | 人工录入
**(仅当题目为人工判题)** | **记分组人员** | 在专用的“人工裁判终端”上，会看到**本题所有提交了手写答案的选手列表**。记分组人员可以**自由选择任意选手**，查看其答案图片，并根据现场评判结果点击 [✔ 正确] 或 [❌ 错误] 按钮进行提交。**该操作可重复执行，以修正之前的判分结果**。
当中控员在大屏上点击“放大展示某选手答案”时，记分端同时“高亮”对应的选手。这样，记分组人员的界面上，当前正在被展示和评判的选手条目会自动高亮或置顶，从而极大地降低选错人的风险。 | **[HTTP API 调用]**人工裁判终端向逻辑服务发起 `POST /api/v1/grade/manual` 请求。该接口具备**更新逻辑**：在已存在更新其 `is_correct` 和 `score` 字段。
**Request Body:** `{"id": 12345, "is_correct": 1, "score": 20}` (id为`答题记录表`的唯一ID) | **1. 逻辑服务闭环处理:**   
- a. 收到 API 请求，根据 `id` 在 NocoDB `答题记录表` 中更新判分结果。
- b. 在 Redis 排行榜中更新对应队伍的分数。
- c. 将这份被人工确认的最终结果，发布到 `{eventid}/result/answer/grading/update` 主题。
**2. 实时反馈与界面解耦:**   
- **大屏实时反馈:** 大屏在任何时候收到 `grading/update` 消息，都会在选手列表或对应区域**实时显示该选手的得分状态**（如叠加✔/❌特效），即使当前大屏焦点在另一位选手上。   
- **界面解耦:** 记分组端的操作独立于中控端的“现场展示”指令。中控员切换大屏展示的选手，不会影响记分组端的界面和操作。 |
| **11** | **循环展示与并行评判
(仅当题目为主观题时)** | **中控员** | 中控员点击【下一位】或选择特定选手，以**控制大屏上展示的答案**，引导现场主持人和评委的注意力。此操作**仅影响大屏显示**，不影响记分组人员的独立评分过程。 | **(同 T+28s 的指令)**  **[指令]** 中控端再次向 `{eventid}/command/display/handwriting/show` 发布消息，但 Payload 中为下一位选手的ID。 | **1. 系统响应:**  
- 系统流程**返回到 T+28s 的“现场展示与评判”步骤**，大屏上显示出新选手的答案。  
- “现场评判 -> 人工录入”的流程由记分组人员并行、独立地完成，直到本题所有选手都被评分。 |
| 12 | 结束本题/进入下一题 | **中控员** | **本题所有选手均已评判完毕。** 中控员点击【第 N 题】进入下一题的流程；或在所有题目结束后，点击【显示排行榜】。 | (同上) | **1. 系统响应:**
- 收到下一题指令后，记分组端的人工判分界面消失。- 若下一题仍是人工判题，记分组端将会在收到新的`adjudication/task/new`消息后，再次进入判分流程。 |

### 2.2 挑战题阶段

本环节题目数量与参赛队伍数量相等，每支队伍均有且仅有一次挑战机会。挑战权按队伍编号顺序依次授予。
轮到挑战的队伍（挑战方）可现场选择由自己作答，或是指定任一其他队伍（作答方）进行作答。
系统将根据所选题目自身的 `question_type` 属性，自动进入“自动判分”或“人工判分”流程。

**计分规则:**

- **自我挑战模式 (挑战方 = 作答方):** 答对，挑战方得分；答错，不得分。
- **指定对手模式 (挑战方 ≠ 作答方):** 对手（作答方）答对，则对手得分；对手答错或未作答，则挑战方得分。
- 分值：20 分

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | **准备挑战** | **中控员** | 1. 在中控台选定【挑战方】和【作答方】。
2. 点击【发起挑战校验】按钮。 | **[新指令]** 中控端向一个新的主题发布消息：`{eventid}/command/quiz/challenge/prepar`
**Payload:**`{"challenger_id": 1001, "challenged_id": 1005}` | **1. 前端响应:**
- **大屏端**监听到 `prepared` 消息，立即显示醒目的校验信息：“即将由【A队】挑战【B队】，请确认！”
- **中控端**也监听到此消息，将【发起挑战校验】按钮变为【确认并出题】，并锁定队伍选择。 |
| 2 | **确定挑战/出题** | **中控员** | 看到大屏信息无误后，点击【确认并出题】按钮。 | **[指令]** 中控端向 `{eventid}/command/quiz/question/switch` 发布消息。**Payload:** `{"question_id": 282, "challenger_id": 1001, "challenged_id": 1005}`
*(注: 若自我挑战，则 `challenger_id` 与 `challenged_id` 相同)* | **1. 逻辑服务响应:**  
- **权限校验:** 检查Redis，确认 `challenger_id` (1001) 尚未行使其挑战权。
- 在 Redis 状态中记录本次挑战的 `challenger_id` 和 `challenged_id`，并将 `challenger_id` 的挑战资格标记为“`待完成`”。
- 挑战资格的状态分为 `可用`、`进行中`、`已使用`只有当一个挑战流程（无论答对、答错、超时或被仲裁）完整结束后，才将状态从 `进行中` 置为 `已使用`
- 从 NocoDB `题目表` 查询题目完整信息 (`question_type`等)。
- 向 `{eventid}/result/quiz/question/update` 发布完整的题目数据。
- 在发布 `.../result/quiz/question/update` 消息时，`Payload` 中除了题目数据，还应包含当前的挑战信息，如：`"challenger_name": "A队"`, `"challenged_name": "B队"`
**2. 前端响应:**  - 所有前端都收到题目数据并显示。 
- **只有作答方 (`challenged_id`: 1005) 的选手端**会根据收到的权威状态，激活答题界面并开始倒计时。其他所有队伍的选手端仅显示题目，界面为非作答状态。 |
| 3 | 选手答题 | **作答方选手** | 在平板上选择（或手写）答案，并**在本地设备上暂存**该选择，等待统一提交指令。 | **(无即时系统交互)** - 选手的作答数据仅保存在选手端设备的内存或本地存储中，尚未向服务器提交。 **实现本地持久化存储**：为防止应用闪退等意外，应将答案写入设备的本地存储（如Local Storage），而不仅是内存。 | **1. 前端响应:**  
- 选手端界面实时反馈选手的选择（例如选项高亮），倒计时结束前可更改。
- 大屏在显示题目的同时，必须在醒目位置清晰地展示“**挑战方：A队**”和“**作答方：B队**”的字样，让所有人都一目了然。 |
| 4 | **时间到/强制提交** | **中控员/系统** | 10秒倒计时结束，或中控员在倒计时期间点击【强制提交】。 | **[前端触发与重试机制]** 
- 收到`{eventid}/command/quiz/answer/submit_now` 指令后，**立即将在本地暂存的答案**采用**带重试机制的降级策略**上报。若首次上报失败，客户端将自动在后台**多次重试**。 
- 上报的 MQTT 消息使用 **QoS 2** 服务质量等级确保送达。 
**[事件上报]** 
选手端向 `{eventid}/event/answer/submit` 发布消息。
**[Webhook触发]** 
EMQX 通过 Webhook 将消息同步请求到逻辑服务。 | **1. 前端响应:**  
- 作答方的选手端答题界面被锁定。 
**2. 逻辑服务根据 `question_type` 自动路由处理流程：
情况A (自动判题):**
- a. 收到答案后，**立即执行“计分核心逻辑”** (见下一行)。
- b. 将包含得分结果的最终判分状态发布到 `{eventid}/result/answer/grading/update`。
**情况 B (人工判题):**
- a. 收到答案后，在`答题记录表`插入记录，标记为`“人工判分”`。
- b. 向 `{eventid}/event/adjudication/task/new` 主题发布待办任务。**此阶段不计分**。
**3. 流程健壮性 (超时处理):**
- 若在指定窗口期后逻辑服务仍未收到作答方的答案，系统会将其标记为“提交异常”，现场工作人员介入处理，确保流程不被阻塞。
- 一旦出现“提交异常”，现场应**立即启动《特殊情况处理（修订版）》中的“仲裁补录流程”**。主持人宣布暂停，工作人员上台确认选手答案，仲裁长在专用终端进行补录。补录成功后，系统会自动冲销之前的默认判分，并根据补录的答案重新计分和更新榜单 |
| 5 | **计分核心逻辑** | **逻辑处理服务 (Logic Handler)** | (由“强制提交”或后续的“人工录入”触发) | **[内部业务逻辑]** | **1. 获取挑战信息:** 从Redis/Nocodb查出本题的 `challenger_id` 和 `challenged_id`。
**2. 判断挑战模式并计分:**   
- **如果 `challenger_id` == `challenged_id` (自我挑战):**     
- 若 `is_correct` 为 `1`，为 `challenger_id` 加分。
- 若 `is_correct` 为 `0`，不加分。
- **如果 `challenger_id` != `challenged_id` (指定对手):**     
- 若 `is_correct` 为 `1`，为 `challenged_id` (作答方) 加分。
- 若 `is_correct` 为 `0`，为 `challenger_id` (挑战方) 加分。
**3. 数据持久化与广播:**   
- 使用 `ZINCRBY` 命令更新 Redis 排行榜。
- 将得分结果更新回 NocoDB `答题记录表`。
- 向 `{eventid}/result/rank/all/update` 发布最新排行榜。 |
| 6 | 现场展示**(仅当题目为人工判题)** | **中控员** | 点击【显示手写答案】。 | **[指令]** 中控端向 `{eventid}/command/display/handwriting/show` 发布消息。 | **1. 前端响应:**   
- 大屏端展示提交答案的选手的手写图片，供现场主持人和评委评判。 |
| 7 | 现场评判**(仅当题目为人工判题)** | **主持人/评委** | 口头宣布答案正确或错误。 | (无系统交互) | (无系统交互) |
| 8 | 人工录入**(仅当题目为人工判题)** | **记分组人员** | 在专用的“人工裁判终端”上，看到待办任务列表，根据现场评判结果，为该条记录点击 [✔ 正确] 或 [❌ 错误] 按钮。
提交判分结果后
弹出一个提示：“**评判录入成功！根据挑战规则，【挑战方A队】得分。**” 这样可以闭环记分员的操作，让他们明确知道自己的操作触发了正确的系统计分逻辑 | **[HTTP API 调用]** 人工裁判终端向逻辑服务发起 `POST /api/v1/grade/manual` 请求。
**Request Body:** `{"id": 12345, "is_correct": 1, "score": 20}` (id为`答题记录表`的唯一ID) | **1. 逻辑服务闭环处理:**   
- a. 收到 API 请求，根据 `id` 更新 NocoDB `答题记录表` 的 `is_correct` 和 `score`。
- b. 根据规则，为**对手**或**本队**在 Redis 排行榜中增加20分。   
- c. 将这份被人工确认的最终结果，发布到 `{eventid}/result/answer/grading/update` 主题。
**2. 实时反馈:**   
- 大屏端监听到 `grading/update` 消息，可在当前展示的手写图片上实时叠加一个“✔”或“❌”的视觉特效。 |
| 9 | **循环/进入下一挑战** | **中控员** | 点击【下一挑战】，**自动高亮推荐的下一支队伍**，但应允许中控员手动选择其他**任何尚未挑战过**的队伍，重复T+0的流程。 | (同上) | (同上) |
| 10 | 公布排行榜 | **中控员** | 点击【显示排行榜】 | **[指令]** 中控端向 `{eventid}/command/display/rank/show` 发布消息。
**[逻辑服务主动推送]** 逻辑服务在每次分数变动后，都会主动从 Redis 读取最新排行，并向 `{eventid}/result/rank/all/update` 发布。 | **1. 逻辑服务响应:**   
- 聚合`答题记录表`等数据，生成包含`score_details`的完整排行榜数据，并向 `{eventid}/result/rank/all/update` 发布。
**2. 前端响应:**   
- 大屏监听到 `rank/show` 指令后，显示排行榜界面。排行榜数据来源于对 `rank/all/update` 的持续订阅。 |

---

## 三、 一站到底环节

本环节整体流程与“有问必答-必答题”阶段类似，但核心区别在于引入了与选手“复活机会”联动的淘汰机制。系统将根据选手的答题正误，自动更新其剩余复活机会，并实时广播状态，实现答错即淘汰或轮换的效果。

本环节共15题，每题10分。题目**只使用客观题**（如选择题、判断题），这类题目可以由系统自动、即时地判分。

**核心淘汰逻辑:**

- **赛前设置:** 在赛事初始化时，根据本环节规则为每位上场选手在 Redis (`event:{eventid}:player:{userId}`) 中设置初始 `revival_chances` (复活机会)。
- **答题错误处理:** 当选手回答错误时，逻辑处理服务会原子性地将其 `revival_chances` 减 1。
- **状态广播与前端响应:** 逻辑服务会立即将选手更新后的状态（包含剩余复活机会）通过 `{eventid}/result/player/status/update` 主题广播出去。选手端或大屏根据收到的 `revival_chances` 判断选手是否被淘汰，并锁定其答题界面。

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | 进入环节 | **主持人/中控员** | 主持人宣布环节开始。中控员点击【一站到底】，再点击【开始】。 | **[指令]** 中控端向 `{eventid}/command/quiz/session/start` 发布消息。
**Payload:** `{"session_id": "one_stand_endurance_01", "session_name": "一站到底"}` | **1. 逻辑服务响应:**
- 在 Redis 状态哈希表 (`event:{eventid}:state`) 中更新 `current_session` 为 `one_stand_endurance_01`。
**2. 前端响应:**
- 大屏、选手端等切换到“一站到底”环节的待机界面。 |
| 2 | 显示规则 | **中控员** | 点击【显示规则】。 | **[指令]** 中控端向 `{eventid}/command/display/content/show` 发布消息。
**Payload:** `{"info_id": 2, "info_type": "规则介绍"}` | **1. 逻辑服务响应:**- 收到指令，根据 `info_id` 从 NocoDB `赛事素材表` 查询规则详情。
- 向 `{eventid}/result/display/content/update` 发布包含规则的权威数据。
**2. 前端响应:**
- 大屏端监听到 `content/update` 消息，显示环节规则。 |
| 3 | 出第1题 | **中控员** | 点击【第 1 题】。 | **[指令]** 中控端向 `{eventid}/command/quiz/question/switch` 发布消息。
**Payload:** `{"question_id": 301, "question_number": 1}` | **1. 逻辑服务响应:**- 更新 Redis 中的 `current_question_id`。
- 从 NocoDB `题目表` 查询该题的完整信息。
- 向 `{eventid}/result/quiz/question/update` 发布完整的题目数据。
**2. 前端响应:**
- 所有**未被淘汰**的选手端、大屏、中控台收到题目数据，并同步显示题目内容。选手端答题按钮激活并开始倒计时。 |
| 4 | 选手答题 | **选手** | 在平板上选择（或手写）答案，并在本地设备上暂存。 | **(无即时系统交互)**
- 选手的作答数据保存在本地存储中，等待统一提交指令。 | **1. 前端响应:**
- 选手端界面实时反馈选手的选择，倒计时结束前可更改。 |
| 5 | 时间到/强制提交 | **中控员/系统** | 倒计时结束，或中控员点击【强制提交】。 | **[事件上报]** 选手端向 `{eventid}/event/answer/submit` 发布消息，使用 QoS 2 服务质量等级，并采用重试机制。
**[Webhook触发]** EMQX 通过 Webhook 将消息同步请求到逻辑服务的 `POST /api/v1/hooks/emqx` 接口。 | **1. 前端响应:**
- 所有作答选手端的答题界面被锁定。
**2. 逻辑服务响应 (核心淘汰逻辑):**
- **情况A (回答正确):**  
- a. 正常判分，使用 `ZINCRBY` 更新 Redis 排行榜。
- b. 更新 NocoDB `答题记录表`。
- c. 广播 `{eventid}/result/answer/grading/update` 结果。
- **情况B (回答错误):**  
- a. 在 `答题记录表` 记为错误，`score` 为0。
- b. **执行复活机会更新**: 使用 Redis 的 `HINCRBY` 命令对 `event:{eventid}:player:{userId}` 这个 Hash 中的 `revival_chances` 字段执行 `-1` 操作。
- c. **广播选手状态**: 逻辑服务立即读取该选手更新后的状态（如剩余复活次数），并向 `{eventid}/result/player/status/update` 主题发布一条消息。
- d. **异步持久化**: 将 Redis 中最新的 `revival_chances` 同步回写到 NocoDB 的`选手表`中。
**3. 前端状态更新:**- **大屏/选手端**订阅 `{eventid}/result/player/status/update`。若收到消息中该选手的 `revival_chances` 已小于等于0，则其界面上会显示“已淘汰”状态，且后续不再接收新题目。 |
| 6 | **公布正确答案** | **中控员** | 点击【公布本题答案】。 | **[指令]** 中控端向 `{eventid}/command/display/question/show_answer` 发布消息。 | 1**. 前端响应:** 大屏监听到 `show_answer` 指令，清晰地展示出本题的标准答案，为主持人解说和选手确认提供依据。 |
| 7 | **公布作答详情与淘汰结果** | **中控员** | 点击【显示选手答案和正误】。 | **[指令]** 中控端向 `{eventid}/command/display/question/reveal_all` 发布空载荷消息。 | **1. 前端响应:**   
- 大屏监听到 `reveal_all` 指令，触发UI动画，将“静默收集”阶段已预先获取的所有选手的答案和正误情况（用红绿色块区分）同时、瞬间地展示出来。
- 同时，根据已收到的 `player/status/update` 消息，对于 `revival_chances` 耗尽的选手，大屏会播放淘汰动画或将其置灰，并锁定其后续答题资格。 |
| 8 | 循环出题 | **中控员** | 主持人完成串场后，中控员点击【第 N 题】，为所有**未被淘汰**的选手开启下一轮答题。 | (同上) | **系统响应:**
- 当收到新的出题指令时，只有 `revival_chances` 大于0的选手才能继续作答。
- 环节在15题全部回答完毕或所有选手均被淘汰后结束。 |
| 9 | 公布排行榜 | **中控员** | 点击【显示排行榜】 | **[指令]** 中控端向 `{eventid}/command/display/rank/show` 发布消息。
**[逻辑服务主动推送]** 逻辑服务在每次分数变动后，都会主动从 Redis 读取最新排行，并向 `{eventid}/result/rank/all/update` 发布。 | **1. 逻辑服务响应:**   
- 聚合`答题记录表`等数据，生成包含`score_details`的完整排行榜数据，并向 `{eventid}/result/rank/all/update` 发布。
**2. 前端响应:**   
- 大屏监听到 `rank/show` 指令后，显示排行榜界面。排行榜数据来源于对 `rank/all/update` 的持续订阅。 |

---

## 四、 争分夺秒环节

本环节共30题，限时180秒，答对得分，可跳过。最终得分 = 答对题数得分 + 剩余时间 * 0.1。

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | **进入环节** | **主持人/中控员** | 主持人宣布环节准备。中控员点击【争分夺秒】。 | **[指令]** 中控端向 `{eventid}/command/quiz/session/start` 发布消息。
**Payload:** `{"session_id": "sprint_180", "session_name": "争分夺秒"}` | **1. 逻辑服务响应:**    
- 更新 Redis 状态，标记当前环节为 `sprint_180`。
**2. 选手端响应 (核心变化):**   
- 收到 `session/start` 指令后，**立即在后台静默调用 `GET /api/v1/events/{eventId}/sessions/sprint_180/questions` 接口**。  
 - **一次性获取本环节所有题目，并缓存在设备本地**。   
- 界面显示“题目加载完毕，请等待比赛开始”的提示。 |
| 2 | **开始计时** | **中控员** | 点击【开始计时】。 | **[指令]** 中控端向 `{eventid}/command/timer/race/start` 发布消息。
**Payload:** `{"duration": 180}` | **1. 逻辑服务响应:**    
- 启动内部计时器，并开始周期性广播权威时间。
**2. 前端响应:**    
- 大屏和所有选手端同步显示180秒倒计时。   
- 选手端**从本地缓存中读取并显示第1题**，比赛正式开始。 |
| 3 | **快速答题与实时进度展示** | **选手 / 大屏** | 选手快速答题。大屏实时展示动态排行榜。 | **[事件上报]** 选手端向 `{eventid}/event/answer/submit` 发布答案。
**[逻辑服务处理]**
1. 高速判分。
2. 更新**专用于本环节**的Redis缓存（分数、进度）。
3. **向专用Topic `{eventid}/result/rank/sprint/update` 发布轻量化排行榜数据。
[前端响应]**
1. 选手端提交后立刻收到下一题。
2. **大屏端订阅专用Topic `{eventid}/result/rank/sprint/update`**，根据收到的数据实时更新一个包含**分数、答题进度、答对题数**的动态排行榜。 | **1. 前端响应:**
- 选手端无缝衔接答题。
- 大屏端展示一个信息更丰富的实时排行榜，
例如：“海事大学、当前得分、已答题数、答对题数“ |
| 4 | **时间到，等待最终计算** | **系统** | 180秒倒计时结束。 | **[逻辑服务内部事件]** 内部计时器检测到时间耗尽。 | **1. 逻辑服务响应:**
- 停止所有选手的答题流程。
- **等待中控员的最终计算指令。
2. 前端响应:**
- 所有选手端界面锁定。
- **大屏上的实时排行榜定格在最后一刻的状态。** |
| 5 | **公布最终得分** | **中控员** | 主持人宣布答题结束。中控员点击【计算并公布最终成绩】按钮。 | **[新增指令]** 中控端向 `{eventid}/command/quiz/sprint/finalize_score` 发布消息。 | **1. 逻辑服务响应:**
- a. 计算每队的“剩余时间奖励分”。
- b. 将“答题分”和“奖励分”合并，并**一次性更新到全局总排行榜**中。
- c. **向全局Topic `{eventid}/result/rank/all/update` 发布最终的、包含所有环节得分详情的排行榜。
2. 前端响应:**
- 大屏端监听到 `rank/all/update` 消息，播放结算动画，并刷新为包含本环节最终得分的全局总排行榜。 |
| 6 | (特殊情况)**题包切换** | **中控员** | 紧急情况下，点击【切换备用题包】。 | **[指令]** 中控端向 `{eventid}/command/quiz/pack/switch` 发布切换指令。 | **1. 逻辑服务响应:**   
- 处理分数冲销，并在 Redis 中更新当前环节应使用的 `pack_id`。
 - **向 `{eventid}/result/quiz/pack/switched` 发布切换成功的回执**。
**2. 选手端响应:**   
- 监听到 `pack/switched` 消息。
- **立即清空本地的题目缓存**。   
- **重新调用 `GET .../questions` 接口**，获取并缓存备用题包的新题目。   
- 等待中控员指令（如重置比赛）以继续。 |

---

## 五、 安全公开课环节 (评委打分)

本环节中，各队得分由现场评委打分决定。最终得分去掉所有评委打分中的一个最高分和一个最低分后，取剩余分数的算术平均值，再根据排名赋分。

### **计分细则与特殊情况处理**

为确保计分过程的绝对公平与透明，特对计分细则补充说明如下：

1. **核心算法**：每位选手的最终得分 = (所有评委打分总和 - 一个最高分 - 一个最低分) / (评委总人数 - 2)。
2. **并列分数处理原则**：
    - 若有多位评委给出了相同的最高分，在计算时**仅去除其中一个**。
    - 若有多位评委给出了相同的最低分，在计算时**仅去除其中一个**。

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | 队伍表演 | **选手** | 某支队伍上台进行表演。 | (无系统交互) | (无系统交互) |
| 2 | 开启打分 | **中控员** | 待表演结束后，选择当前表演的队伍，点击【开启评委打分】。 | **[指令]** 中控端向 `{eventid}/command/judge/entry/start` 发布消息。
**Payload:** `{"user_id": 1001}` | **1. 逻辑服务响应:**   
- 在 Redis 中记录 `judging_user_id: 1001`。
**2. 前端响应:**   
- 所有评委端收到指令，界面解锁，并显示“正在为[复旦大学]评分”。 |
| 3 | **评委实时打分** | **评委** | 在评委端输入分数，点击【提交】。 | **[事件上报]** 评委端向 `{eventid}/event/judge/submit` 发布消息。
**Payload:** `{"judge_id": 2001, ...}`
**[逻辑服务处理]**
1. 持久化并暂存评分。
2. **立即向 `{eventid}/result/judge/score/submitted` 广播实时打分。** | **1. 前端实时响应 (大屏):** 
大屏订阅 `.../score/submitted` Topic，每收到一个包含 `judge_name` 和 `judge_score` 的消息，就在屏幕的‘评委打分栏’中动态展示一个独立的评分卡片，卡片顶部显示评委的匿名代号（如‘评委 1’），底部显示其给出的分数（如‘98.5’），实现分数逐个弹出的实时效果。 |
| 4 | **评委修正评分** | **评委** | 在发现分数输入错误后，**在打分通道关闭前**，在评委端重新输入分数，再次点击【提交】。 | **[事件上报]** 评委端向与首次提交**完全相同**的 Topic `{eventid}/event/judge/submit` 再次发布消息。
 **Payload:** `{"judge_id": 2001, "user_id": 1001, "judge_score": 95.5}` (新的分数) | **1. 逻辑服务响应 (覆盖更新):**  
- 服务根据 `judge_id` 和 `user_id` 识别出这是同一位评委对同一位选手的重复提交。
- **执行覆盖逻辑**：在 Redis 缓存和 NocoDB `评委评分表`中，用新分数**更新**该评委之前的评分记录，而不是插入新纪录。  
**2. 前端实时响应 (大屏):**  
大屏订阅 `.../score/submitted` Topic，每收到一个包含 `judge_name` 和 `judge_score` 的消息，就在屏幕的‘评委打分栏’中动态展示一个独立的评分卡片，卡片顶部显示评委的匿名代号（如‘评委 1’），底部显示其给出的分数（如‘98.5’），实现分数逐个弹出的实时效果。” |
| 5 | 结束打分 | **中控员** | 点击【关闭评委打分】。 | **[指令]** 中控端向 `{eventid}/command/judge/entry/end` 发布空载荷消息。 | **1. 前端响应:**   
- 所有评委端界面锁定。 |
| 6 | **计算并更新排行** | **中控员** | 点击【结束评分并计算】。 | **[指令]** 中控端向 `{eventid}/command/judge/score/calculate` 发布消息。<br>
**Payload:**
- **正常情况**：`{}` (空对象)
- **强制计算时**：`{"force": true}` | **情况A (正常流程):** 
逻辑服务校验通过，完成计算并广播最终排行榜。
**1. 逻辑服务响应:**
a. 计算最终赋分。
b. **同时更新**全局排行榜和**环节独立排行榜** (`leaderboard:judge_session`)。
c. **同时广播**三个结果：   
- `{eventid}/result/judge/score/update` (赋分详情)
- `{eventid}/result/rank/all/update` (全局总排行)
- **`{eventid}/result/rank/judge_session/update` (环节独立排行)**
**情况B (异常流程 - 提交不完整):**
1. **逻辑服务拦截**：服务检测到评分数量不足，**暂停计算**，并向中控端发送系统通知。
2. **中控端响应**：界面弹出警告框：“**警告：仅收到 X/Y 位评委的评分，是否强制计算？**”
3. **人工决策**：中控员点击【强制计算】。
4. **强制执行**：中控端再次发送`calculate`指令，但Payload中增加`{"force": true}`标记。
5. **完成计算**：逻辑服务收到强制指令，完成计算并广播结果。 |
| 7 | **显示本环节结果** | **中控员** | 点击【显示最终评分】。 | **[指令]** 中控端向 `{eventid}/command/display/rank/show_final` 发布消息。 | **1. 前端响应 (大屏):**
a. 大屏监听到指令，播放结算动画。
b. 根据收到的 `.../judge/score/update` 数据，在右侧展示去掉最高分、最低分的计算过程和最终平均分。
c. 根据收到的 **`.../rank/judge_session/update`** 数据，将最终分数更新到**左侧表演列表**中对应的队伍栏。**此时列表顺序维持不变**。 |
| 8 | **循环与结束** | **中控员** | 重复以上流程，直到所有队伍表演并评分完毕。 | (同上) | (同上) |
| 9 | **最终环节排序** | **中控员** | 所有队伍评分展示完毕后，主持人串场，中控员点击【本环节排序】按钮。 | **[新增指令]** 中控端向 `{eventid}/command/display/rank/sort_now` 主题发布消息。
**Payload:** `{"session_id": "security_open_class"}` | **1. 前端独立消费:** 
- 此指令主要由**大屏端**等前端应用直接监听和消费。
- **后端逻辑服务无需处理**此指令，符合关注点分离原则。
**2. 前端响应:** 
- 大屏端监听到 `sort_now` 指令，对自己内部缓存的本环节排行榜数据（数据来源于对 `result/rank/judge_session/update` 的持续订阅）进行**客户端排序**。
- 播放一个华丽的排序动画，将队伍列表从固定的出场顺序重新排列为按本环节得分高低的最终排名。 |
| 10 | 公布总排行榜 | **中控员** | 点击【显示排行榜】 | **[指令]** 中控端向 `{eventid}/command/display/rank/show` 发布消息。
**[逻辑服务主动推送]** 逻辑服务在每次分数变动后，都会主动从 Redis 读取最新排行，并向 `{eventid}/result/rank/all/update` 发布。 | **1. 逻辑服务响应:**   
- 聚合`答题记录表`等数据，生成包含`score_details`的完整排行榜数据，并向 `{eventid}/result/rank/all/update` 发布。
**2. 前端响应:**   
- 大屏监听到 `rank/show` 指令后，显示排行榜界面。排行榜数据来源于对 `rank/all/update` 的持续订阅。 |

---

## 六、 终极 PK 环节 (辩论赛)

两队对决，分阶段计时。

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | 切换阶段 | **中控员** | 点击【观点陈述】阶段。 | **[指令]** 中控端向 `{eventid}/command/pk/phase/switch` 发布消息。
**Payload:** `{"phase_id": "opening_statement"}` | **1. 逻辑服务响应:**   
- a. 收到精简指令，从 Redis/NocoDB `环节表`的`session_config`中查询该`phase_id`的**权威配置**（名称、总时长、提醒阈值）。   
- b. 将完整的阶段信息和重置后的计时器状态更新到 Redis。   
- c. **广播权威状态:** 立即向 `{eventid}/result/timer/pk_state/update` 发布完整的PK状态。
**Payload:** `{"global_status": "running", ..., "current_phase": {"phase_id": "...", "phase_name": "观点陈述", "duration": 90, ...}}` |
| 2 | 控制计时 | **中控员** | 主持人宣布正方开始，中控员点击正方的【开始计时】按钮。 | **[指令]** 中控端向 `{eventid}/command/timer/pro_team/control` 发布消息。
**Payload:** `{"status": "start"}` | **1. 逻辑服务响应:**   
- a. 记录精确的 `startTime`，启动内部计时器。
- b. 周期性向 `{eventid}/result/timer/pk_state/update` 广播权威状态。
- c. 当服务判断剩余时间到达 `reminder_at` 阈值时，在当次广播的 Payload 中将 `is_reminding` 标记为 `true`
**2. 前端响应:**   
- a.大屏、中控等所有客户端只订阅 `result/timer/pk_state/update`，根据收到的权威数据来更新自己的倒计时显示。
- b. **所有前端应用**监听到消息后，检查 Payload 中的 `is_reminding` 字段。**如果为 `true`**，则各自触发相应的提醒效果（例如：中控端播放提示音、大屏端计时器变为红色并闪烁等）。 |
| 3 | 暂停/切换发言方 | **中控员** | 点击【暂停】或【切换发言方】。 | 向 `{eventid}/command/timer/global/control` 或 `{eventid}/command/pk/turn/switch` 发布消息。 | **1. 逻辑服务响应:**   - 更新 Redis 中的计时器状态 (`global_status`) 或当前发言方 (`active_team`)。
- 立即将更新后的完整状态广播到 `{eventid}/result/timer/pk_state/update`。 |
| 4 | 循环 | **中控员** | 重复“切换阶段”->“控制计时”的流程，完成所有辩论环节。 | (同上) | (同上) |
| 5 | **开启投票通道** | **中控员** | 主持人宣布进入投票环节。中控员点击【开启PK投票】。 | 中控端向 `{eventid}/command/pk/vote/start` 发布消息。
**Payload:** `{"pro_team_id": 1001, "con_team_id": 1002, "pro_team_name": "A队", "con_team_name": "B队"}` | **1. 逻辑服务响应:**  
- 在 Redis 状态中记录当前进入PK投票阶段，并记下正反方信息。
**2. 前端响应:**  
- **所有评委端、大众评委端（选手端改造）** 收到指令，界面切换为投票页，显示“请为 [A队] 或 [B队] 投票”的按钮。  
- **大屏**切换到投票数据实时展示界面。 |
| 6 | **评委/大众投票** | **专家评委 / 大众评委** | 在各自的终端上点击支持的队伍。 | 各终端向 `{eventid}/event/pk/vote/submit` 发布投票事件。
**Payload (专家):** `{"voter_id": 2001, "voter_type": "expert", "voted_for_team_id": 1001}`
**Payload (大众):** `{"voter_id": "device_vwxyz", "voter_type": "public", "voted_for_team_id": 1002}`

**特别说明：** 系统后端采用**首次提交锁定机制**。每位投票者（由`voter_id`唯一标识）的第一次投票会被系统记录，后续所有重复的投票请求都将被自动忽略，**无法修改投票**。 | **1. 逻辑服务处理:** 
a. 根据 `voter_id` 执行幂等性检查，确保投票唯一性。
b. 若为首次投票，则根据 `voter_type` 确定分值，在 NocoDB `投票记录表`中插入记录，并更新 Redis 实时总分。
**2. 实时结果广播:** 
- 逻辑服务向 `{eventid}/result/pk/vote_progress/update` 发布实时票数。
**Payload:** `{ "pro_team": {"total_votes":12, "total_score":32, "expert_votes":4, "expert_score":20, "public_votes":8, "public_score":12}, "con_team": {"total_votes":9, "total_score":29, "expert_votes":5, "expert_score":25, "public_votes":4, "public_score":4} }`
**3. 大屏实时响应:** 
- 大屏订阅 `vote_progress/update`，每收到一次更新，就可以根据Payload中详细的票数（如 `expert_votes`, `public_votes`）和分数，实时刷新一个包含多种维度（如专家支持度、大众支持度）的、更丰富的票数对比图。 |
| 7 | **关闭投票通道** | **中控员/系统** | 主持人口头倒计时结束，中控员点击【关闭PK投票】。 | 中控端向 `{eventid}/command/pk/vote/end` 发布空载荷消息。 | **1. 逻辑服务响应:**  
- 停止接收任何新的投票事件。
**2. 前端响应:**  
- 所有投票终端界面锁定，显示“投票已结束”。 |
| 8 | **计算并裁定最终冠军** | **中控员** | 主持人串场，中控员点击【宣布PK结果】。 |  中控端向 `{eventid}/command/pk/result/calculate` 发布消息。 | **1. 逻辑服务执行“冠军裁定逻辑” (详见逻辑处理服务文档)**  
a. 从 Redis 读取 PK 投票的最终得分。 
b. 从总排行榜 (`event:{eventid}:leaderboard`) 读取两队PK前的总分。
c. **执行智能加分算法**，确保 PK 胜者总分一定高于败者。 
d. **一次性更新** Redis 总排行榜。
**2. 结果广播:** 
- 逻辑服务向 `{eventid}/result/rank/all/update` 发布包含最终冠亚军排名的全局总排行榜。 |
| 9 | **公布最终结果** | **中控员** | 点击【显示最终排行榜】。 | **[指令]** 中控端向 `{eventid}/command/display/rank/show` 发布消息。 | **1. 前端响应:** - 大屏监听到 `show` 指令，并根据收到的最新 `rank/all/update` 数据，播放加冕动画，展示冠亚军队伍及其最终总分。 |

---

## 七、闭幕式与颁奖

**环节目标：** 在所有比赛环节结束后，公布最终获奖名单，进行颁奖，并由领导进行总结发言，为整个赛事画上圆满的句号。

**前置条件：**

1. 所有比赛环节均已结束，逻辑服务已根据所有得分计算出最终的全局排行榜。
2. 颁奖相关的素材，如各奖项名称（三等奖、二等奖、一等奖、冠军）、对应的奖杯或奖状图片等，已预先录入 `赛事素材表`。

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | **公布奖项** | **中控员** | 主持人宣布进入颁奖环节。中控员点击【公布三等奖】。 | **[指令]** 中控端向 `{eventid}/command/display/content/show` 发布消息。
**Payload:** `{"info_type": "award_announcement", "award_rank": "third_prize"}` | **1. 逻辑服务响应:**
- 收到指令，从 Redis 的最终排行榜 (`event:{eventid}:leaderboard`) 中，查询出符合该奖项等级的队伍列表。
- 从 `赛事素材表` 查询奖项名称（如“三等奖”）和背景图。
- 将奖项名称和获奖队伍列表组合，向 `{eventid}/result/display/content/update` 发布数据。
**2. 前端响应:**
- **大屏端**播放颁奖动画，随后显示奖项名称（如“三等奖”）和所有获奖队伍的名单。 |
| 2 | **循环颁奖** | **中控员** | 在一个奖项颁发完毕后，主持人宣布下一个奖项。中控员依次点击【公布二等奖】、【公布一等奖】、【公布冠军】。 | **[指令]** 中控端重复向 `{eventid}/command/display/content/show` 发布消息，仅 `award_rank` 字段不同。
**Payload (示例):** `{"info_type": "award_announcement", "award_rank": "champion"}` | **1. 系统响应:**
- 系统流程同上，大屏依次展示更高等级的奖项和获奖队伍。
- 当公布冠军时，可以配合播放特殊的加冕动画和背景音乐，营造高光时刻。 |
| 4 | **领导总结** | **中控员** | 颁奖结束后，主持人邀请领导做总结发言。中控员点击【显示领导总结】。 | **[指令]** 中控端向 `{eventid}/command/display/content/show` 发布消息。
**Payload:** `{"info_id": 202, "info_type": "leader_summary"}` | **1. 逻辑服务响应:**
- 根据 `info_id` 从 `赛事素材表` 查询领导的姓名、职位及发言要点（如有）。
- 向 `{eventid}/result/display/content/update` 发布数据。
**2. 前端响应:**
- **大屏端**显示进行总结发言的领导信息。 |
| 4 | **宣布闭幕** | **主持人 / 中控员** | 领导发言完毕，主持人宣布赛事圆满闭幕。中控员点击【宣布比赛结束】。 | **[指令]** 中控端向 `{eventid}/command/display/content/show` 发布消息。
**Payload:** `{"info_type": "event_end_screen"}` | **1. 逻辑服务响应:**
- 从 `赛事素材表` 查询闭幕画面所需素材（如“感谢参与”、“圆满落幕”等字样、主办方Logo等）。
- 向 `{eventid}/result/display/content/update` 发布数据。
**2. 前端响应:**
- **大屏端**显示赛事结束的最终画面，可配以背景音乐。赛事流程完全结束。 |

## 八、 特殊情况处理

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | 单题仲裁作废 | **中控员** | 发现某题有问题，在仲裁后，于中控台选择该题，点击【本题作废】。 | **[指令]** 中控端向 `{eventid}/command/quiz/question/invalidate` 发布消息。
**Payload:** `{"question_id": 126, "reason": "..."}` | **1. 逻辑服务响应:**   
- a. 查询 NocoDB `答题记录表`，找到所有`question_id`为126且`status`为`有效`的记录。
- b. **遍历记录:** 对每条记录，获取`user_id`和`score`，在 Redis 排行榜中为该用户**减去**相应分数 (`ZINCRBY ... -10 ...`)。
- c. 将`答题记录表`中这些记录的`status`批量更新为`“作废”`。
- d. 向 `{eventid}/result/system/notification/show` 发布系统通知：“第5题已作废”。
- e. 向 `{eventid}/result/rank/all/update` 发布冲销分数后的最新排行榜。 |
| 2 | 题包切换 | **中控员** | 发现正式题包大面积问题，申请切换到备用题包。点击【切换备用题包】。 | **[指令]** 中控端向 `{eventid}/command/quiz/pack/switch` 发布消息。
**Payload:** `{"session_id": "qa_mandatory_01", "target_pack_id": 2, "reason": "..."}` | **1. 逻辑服务响应:**   
- a. (同上) 查询并冲销该`session_id`下`所有`有效答题记录的分数。
- b. 将这些记录的 `status` 更新为 `“题包作废”`。
- c. **关键步骤:** 更新 Redis 赛事状态 `HSET event:{eventid}:state pack_id_for_qa_mandatory_01 2`，指定后续出题从此题包获取。
- d. 清理旧题包的 Redis 缓存，并预热新题包的题目数据。
- e. **发送回执:** 向 `{eventid}/result/quiz/pack/switched` 报告切换成功。   
- f. 发布更新后的排行榜和系统通知。 |
| 3 | 赛中紧急修改配置 | 中控员 / 技术支持 | **场景**: 比赛期间，发现某环节（如PK赛）的预设时长不合理，需要紧急调整。
1. 技术支持人员在 NocoDB `环节表` 中找到对应环节，修改其 `session_config` JSON 中的时长数值并保存。
2. **中控员**在中控台的“高级”或“管理”面板中，选择该环节，点击【**重载环节配置**】按钮。 | **[指令]** 中控端向 `{eventid}/command/system/config/reload` 主题发布消息。
**Payload**: `{"session_id": "pk_final_debate"}`
**[API 调用备选]** 管理后台向 `POST/api/v1/events/{eventId}/sessions/{sessionId}/reload-config` 发起请求。 | **1. 逻辑服务响应:**收到指令，立即从 NocoDB 查询最新的 `session_config`。
- 将新配置覆盖写入 Redis 缓存。
- 向 `{eventid}/result/system/notification/show` 发布成功通知。
**2. 前端响应:**中控台收到通知后，弹出一个绿色的 Toast 提示：“环节 '终极PK' 的配置已成功重载。”
**3. 后续效果:**当该环节的下一个相关指令（如 `pk/phase/switch`）被触发时，逻辑服务将读取并使用 Redis 中最新的配置（例如，新的计时时长），确保比赛按新规则进行。 |
| 4 | 全局数据同步/缓存重置 | 管理员 / 高级技术支持 | **场景**: 在后台 NocoDB 中对赛事数据进行了大量修改（如导入新选手、批量修正题目分值），或因未知原因怀疑线上实时数据与数据库不符时。
**操作**: 在中控台的“系统管理”或“高级运维”界面，输入要操作的赛事ID，点击【**刷新赛事全部缓存**】按钮。 | **[指令]** 中控端向 `{eventid}/command/system/cache/refresh_all` 主题发布空载荷消息。
**[API 调用备选]** 管理后台向 `POST /api/v1/events/{eventId}/cache/refresh` 发起请求。 | **1. 逻辑服务响应 (后台):**
服务执行一个完整的缓存重建流程： 
a. 清空 Redis 中该赛事的所有基础缓存。 
b. 从 NocoDB 重新加载选手、题目、环节配置等信息到 Redis。 
c. 清空 Redis 中的旧排行榜。 
d. 从 NocoDB 的答题记录和评分记录重新计算所有选手的总分，并重建排行榜。
**2. 前端响应 (中控台):**点击后，按钮可变为“刷新中...”。收到 `{eventid}/result/system/notification/show` 的成功通知后，弹出一个绿色的 Toast 提示：“赛事 {eventId} 的全部缓存已刷新成功。”
- 同时，中控台订阅的排行榜视图会接收到 `{eventid}/result/rank/all/update` 的最新数据，并自动刷新显示。 |

## 九、 异常提交处理与补录流程 (修订版)

本流程旨在通过系统自动甄别与高权限人工干预相结合的方式，精准、公正地处理所有非正常的答题提交情况。核心逻辑在于，对于因技术故障等原因未能成功提交的答案，并非更新已有记录，而是通过一个独立的补录流程**创建一条全新的、带有特殊标记的答题记录**，以保证数据链路的完整性和可审计性。

### 9.1 系统自动甄别与处理

在每个答题提交窗口关闭后，逻辑服务会立即对提交数据进行分析，并根据以下两种情况自动执行不同流程：

| 序号 | 情况描述 | 技术特征 | 系统自动化处理流程 |
| --- | --- | --- | --- |
| 1 | **情况 A: 选手主观放弃** | 后台**能收到**该选手的提交事件，但其 `submitted_answer` 载荷为空 (`null` 或 `""`)。 | **1. 自动判为错误：** 
- 逻辑服务将此行为直接认定为“回答错误”。      
- 在 NocoDB `答题记录表` 中创建一条记录，`is_correct` 记为 `0`，`score` 记为 `0`，并将 `submission_type` 关键字段标记为 `“主观放弃”` 以供审计。 
**2. 触发标准计分：** 
- **立即触发**与正常提交完全相同的后续计分逻辑。      
- 对于**挑战题**，这意味着“挑战方”将自动得分。   
**3. 广播与反馈：** 
- 正常广播 `grading/update` 和 `rank/all/update` 消息。      
- 大屏端在该选手状态栏显示“已提交”或具体的对错反馈。   
**结论：** 此流程**完全自动化**，无需任何人工干预，赛事流程无停顿。 |
| 2 | **情况 B: 潜在技术故障** | 后台在指定时间内**完全没有收到**该选手的任何提交事件（没有对应的 MQTT `event` 或 Webhook 请求）。 | **1. 标记等待补录：** 
- 逻辑服务**不会**对此计分，**不创建任何数据库记录，系统会等待一个**完整的提交窗口（如10秒）结束后，才会而是在内部（如 Redis）将该选手本题的状态标记为 `“提交异常”` 。 
**2. 前端显示异常：** 
- 大屏端在该选手的状态栏明确显示“提交异常”**或高亮警告标识，以提示现场人员。   
结论： 此流程**触发人工补录，赛事将由主持人暂停，进入下述的人工补录流程
3. **系统自动上锁：** 逻辑服务在将该题标记为‘`提交异常`’的同时，会对该题目进行**系统级锁定**，暂时阻止“公布答案”等后续操作，以强制保证仲裁流程的公平性。 |

### **9.2 提交超时与延迟消息处理机制**

此流程旨在通过明确的超时机制和严格的状态检查，系统性地解决因网络延迟等原因导致答题数据在超时后才到达服务器的问题，确保人工补录流程的权威性和数据的一致性。

### **1. 触发与“提交窗口期”的定义**

- **启动并严格执行10秒窗口**：
    - 当逻辑处理服务收到`{eventid}/command/quiz/answer/submit_now`指令后，应为当前题目所有作答方开启一个**完整的、时长为10秒的“提交窗口期”**。
- **窗口期内无差别接收**：
    - 在**整个10秒窗口期内**，逻辑处理服务应**无差别地接收并正常处理**所有到达的`event/answer/submit`事件。无论是第1秒还是第9.9秒到达的提交，都应被视为有效，并立即进入后续的自动或人工判分流程。
- **窗口期结束后统一复盘**：
    - 在10秒窗口期**完全关闭**的时刻，逻辑处理服务才执行一次性的检查。
    - 它会遍历所有应答选手，对于在此10秒内**仍未收到任何提交记录**的选手，此时才将其状态置为`“提交异常”`。
- **明确迟到消息的处理**：
    - 任何在10秒窗口期**结束之后**才到达的提交消息，才被系统确认为“超时无效提交”并执行丢弃策略。

### 9.3 人工补录流程 (仅当“情况B”发生时启动)

此流程作为“记分组终端”应用内的一个补录模块存在，仅在系统监测到“提交记录缺失”时启动。

| 序号 | 环节/阶段 | 执行角色 | 具体动作/指令 | 系统交互/技术触发点 | 预期系统响应/结果 |
| --- | --- | --- | --- | --- | --- |
| 1 | **设备状态与答案确认** | **主持人 /** 记分组 **** | 主持人因大屏显示“提交异常”而暂停流程。
现场工作人员立即上台，询问并确认选手在该题中**最终选择的答案（选手端上会显示已选择的选项）**。**(此步骤必须在该题的“公布正确答案”环节前完成)** | (无系统交互) | 现场所有人员见证了选手在答案揭晓前已确定其选择，保证了公平性。记分组获得需要补录的准确信息。 |
| 2 | **进入补录视图** | 记分组 | 使用**同一个“记分组终端”应用**，在顶部的导航可以进行，判分和补录进行页面切换 | **1.  路由导航： 应用前端根据导航状态，自动将页面重定向**至独立的补录视图/路由（例如 `/arbitration`）。 | **1. 系统响应：** 
- 应用界面展示为“补录”专用视图。    
- 该视图独立于判分界面（`/scoring` 路由） |
| 3 | **执行答案补录** | 记分组 | 在“补录”视图中：
1. 选择当前赛事和环节。
2. 系统自动列出当前状态为“提交异常”的选手和题号。
3. 选中目标选手和题目。
4. 根据现场口头确认的答案，录入其选择（如'B'）。
5. 在“仲裁原因”文本框中**必须填写**原因，例如：“XXX号选手设备于20:15:32发生死机，当场向裁判确认答案为B。”
6. 点击【确认补录】。 | **[专用API调用]** 
补录视图向逻辑服务发起一个专用的的API请求。**Endpoint:** `POST /api/v1/grade/arbitrate`
**Request Body:**`json { "event_id": "...", "question_id": 285, "user_id": 1001, "submitted_answer": "B", "reason": "...", "arbiter_id": "chief_judge_01"  }` | **1. 逻辑服务闭环处理：** 
- a. **数据写入：** 在 NocoDB `答题记录表` 中插入一条新的答题记录。**关键字段标记如下**：        
- `submission_type` 字段标记为 `“记分补录”` 
- 新增的 `audit_details` (JSON类型) 字段存入 `{ "reason": "...", "arbiter_id": "..." }` 等信息。
- b. **触发计分**：记录成功插入后，逻辑服务**立即执行相应的计分逻辑**（见7.3）。
- c. **自动解锁：** 成功处理补录请求后，逻辑服务**自动移除**该题目的系统锁定，允许后续操作（如‘公布答案’）正常进行。
**2. 结果与状态广播：** 
- a. **广播判分结果：** 向 `{eventid}/result/answer/grading/update` 主题发布该选手的判分结果。
- b. **广播排行榜更新：** 向 `{eventid}/result/rank/all/update` 发布更新后的完整排行榜。
**3. 前端实时响应：** 
- 各端监听到广播消息，**自动、实时地**更新该选手的得分与状态，将“提交异常”替换为最终的判分结果。 |
| 4 | **恢复赛事流程** | **主持人 / 中控员** | 记分组示意后台处理完毕。主持人宣布问题已解决，并继续赛事流程（如“公布本题答案”）。 | (同手册中正常流程) | 赛事流程无缝衔接。所有数据均已通过仲裁补录的方式修正，保证了最终结果的准确性和公正性，且整个过程对其他角色的干扰降到了最低。 |
| 5 | 锁定期间的误操作 | 中控员 | 在记分组人员完成答案补录**之前**，误操作点击了【公布本题答案】按钮。 | **[指令]** 中控端向 `{eventid}/command/display/question/show_answer` 发布消息。 | 1. **逻辑服务响应 (拦截)：** 逻辑服务检测到该题目的‘补录锁定’状态，**拒绝执行**该指令。  
2. **结果广播 (明确反馈)：** 逻辑服务向 `{eventid}/result/system/error/notify` 主题发布一条结构化的错误通知。
3. **前端响应：** 中控台界面会收到错误通知，并应立即弹出一个明确的提示框，内容为：“**操作失败：本题有选手提交异常，正在等待记分组补录，暂时无法公布答案。**” |

### 9.4 仲裁补录的计分逻辑特别说明

当逻辑服务收到 `/api/v1/grade/arbitrate` 请求后，其核心任务是根据题目类型，触发**正确的计分模型**。

| 序号 | 补录的题目类型 | 逻辑服务核心处理流程 |
| --- | --- | --- |
| 1 | **普通题 (必答题/抢答题等)** | 收到补录请求后，系统执行标准的计分流程：根据补录的答案与正确答案比对，计算 `is_correct` 和 `score`，更新排行榜。 |
| 2 | **挑战题** | 1.  **关键识别步骤：** 逻辑服务收到请求后，根据 `question_id` 从 Redis 状态缓存中查询到，该题目隶属于“挑战题”环节。
2.  **检索挑战上下文：** 系统**必须**从 Redis 中检索出与该题绑定的 `challenger_id` 和 `challenged_id`。
3.  **触发特定计分逻辑：** 系统**不会**执行标准计分流程，而是**必须触发“挑战题”环节专属的“计分核心逻辑”**：    
-   若补录的答案**正确**，为 `challenged_id` (作答方) 加分。    
-   若补录的答案**错误**，为 `challenger_id` (挑战方) 加分。
4.  **更新与广播：** 根据正确的计分结果，更新 Redis 排行榜，并广播最终结果。 |