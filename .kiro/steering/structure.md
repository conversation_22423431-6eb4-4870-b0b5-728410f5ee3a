# Project Structure

## Recommended Directory Layout

```
logic-handler-service/
├── src/
│   ├── common/              # Shared utilities and decorators
│   │   ├── decorators/      # Custom decorators (@MessageHandler, etc.)
│   │   ├── filters/         # Exception filters
│   │   ├── guards/          # Authentication guards
│   │   ├── interceptors/    # Request/response interceptors
│   │   └── pipes/           # Validation pipes
│   ├── config/              # Configuration modules
│   │   ├── app.config.ts    # Application configuration
│   │   ├── redis.config.ts  # Redis configuration
│   │   └── mqtt.config.ts   # MQTT configuration
│   ├── modules/             # Business modules
│   │   ├── message/         # MQTT message routing and handling
│   │   ├── scoring/         # Auto/manual grading logic
│   │   ├── timer/           # Competition timer management
│   │   ├── state/           # Player and event state management
│   │   ├── leaderboard/     # Ranking and score calculations
│   │   └── cache/           # Redis caching services
│   ├── shared/              # Shared types and utilities
│   │   ├── types/           # TypeScript interfaces and types
│   │   ├── constants/       # Application constants
│   │   └── utils/           # Utility functions
│   ├── app.module.ts        # Root application module
│   └── main.ts              # Application entry point
├── test/                    # Test files
│   ├── unit/                # Unit tests
│   ├── integration/         # Integration tests
│   └── e2e/                 # End-to-end tests
├── docs/                    # Documentation
├── .env.example             # Environment variables template
└── package.json
```

## Module Organization Principles

### Business Modules Structure
Each module in `src/modules/` follows this pattern:
```
module-name/
├── dto/                     # Data Transfer Objects
├── entities/               # Data models/interfaces
├── services/               # Business logic services
├── handlers/               # Message handlers
├── repositories/           # Data access layer
├── module-name.module.ts   # Module definition
└── index.ts                # Module exports
```

### Naming Conventions

- **Files**: kebab-case (e.g., `message-handler.service.ts`)
- **Classes**: PascalCase (e.g., `MessageHandlerService`)
- **Methods/Variables**: camelCase (e.g., `handleAnswerSubmit`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_RETRY_ATTEMPTS`)
- **Interfaces**: Prefix with `I` (e.g., `IScoringService`)
- **Types**: Suffix with `Type` (e.g., `PlayerStateType`)

### Key Architectural Components

#### Message Handlers
- Located in `src/modules/message/handlers/`
- Use `@MessageHandler('topic/pattern')` decorator
- Handle MQTT topic routing and validation

#### Services
- Business logic implementation
- Injectable classes with dependency injection
- Follow single responsibility principle

#### Repositories
- Data access abstraction layer
- Separate Redis and NocoDB operations
- Implement consistent error handling

#### DTOs (Data Transfer Objects)
- Define message payload structures
- Use class-validator decorators for validation
- Follow snake_case for JSON fields (MQTT/API compatibility)

## Configuration Management

### Environment-based Configuration
- Development: `.env` file
- Production: Environment variables or secrets management
- Test: Separate test configuration

### Module Configuration
- Each module exports its configuration
- Global configuration in `src/config/`
- Type-safe configuration with validation

## Testing Structure

### Test Organization
- Mirror source structure in test directories
- Unit tests alongside source files (`.spec.ts`)
- Integration tests in dedicated directories
- E2E tests for complete workflows

### Test Naming
- Describe behavior: `should handle answer submission correctly`
- Use Given-When-Then pattern in test bodies
- Group related tests with `describe` blocks

## Import Path Conventions

Use TypeScript path mapping for clean imports:
```typescript
// Instead of: import { ScoringService } from '../../../modules/scoring/services/scoring.service'
import { ScoringService } from '@/modules/scoring';
import { PlayerState } from '@/shared/types';
import { REDIS_KEYS } from '@/shared/constants';
```

## Documentation Standards

- README.md in each module explaining its purpose
- JSDoc comments for public APIs
- Architecture decision records (ADRs) for major decisions
- API documentation for HTTP endpoints