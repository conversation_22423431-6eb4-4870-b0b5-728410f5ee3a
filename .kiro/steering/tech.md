# Technology Stack

## Core Framework & Runtime

- **Node.js**: v18+ (LTS version)
- **TypeScript**: v5.0+ for type safety
- **NestJS**: v10+ microservices framework with dependency injection
- **Fastify**: v4+ HTTP server for high performance (instead of Express)

## Key Libraries

- **MQTT.js**: v5+ for MQTT client communication with EMQX broker
- **ioredis**: v5+ Redis client for caching and state management
- **Axios**: HTTP client for NocoDB API calls
- **Decimal.js**: High-precision decimal calculations for scoring
- **Class-validator & Class-transformer**: Data validation and transformation
- **Winston**: Structured logging
- **@nestjs/schedule**: Cron jobs and scheduled tasks

## Data Storage

- **Redis**: v7+ for real-time state caching and leaderboards
- **NocoDB**: Database operations via REST API and nocodb-sdk
- **EMQX**: MQTT broker for real-time messaging

## Development Tools

- **Jest**: v29+ for unit and integration testing
- **ESLint & Prettier**: Code quality and formatting
- **<PERSON><PERSON> & lint-staged**: Git hooks for code quality
- **Supertest**: HTTP endpoint testing

## Common Commands

```bash
# Development
npm run start:dev          # Start in watch mode
npm run start:debug        # Start with debugging

# Building
npm run build              # Compile TypeScript
npm run start:prod         # Start production build

# Testing
npm test                   # Run unit tests
npm run test:watch         # Run tests in watch mode
npm run test:cov           # Run with coverage report
npm run test:e2e           # End-to-end tests

# Code Quality
npm run lint               # ESLint check
npm run lint:fix           # Auto-fix linting issues
npm run format             # Prettier formatting
```

## Environment Configuration

Required environment variables (see 环境变量配置文档.md for details):

- `NODE_ENV`: development/production
- `PORT`: HTTP server port (default: 3000)
- `REDIS_URL`: Redis connection string
- `NOCODB_API_BASE_URL`: NocoDB API endpoint
- `NOCODB_API_TOKEN`: NocoDB authentication token
- `MQTT_BROKER_URL`: EMQX WebSocket URL
- `MQTT_USERNAME/PASSWORD`: MQTT authentication
- `API_AUTH_TOKEN`: Internal API protection token

## Architecture Patterns

- **Event-driven**: MQTT message-based async processing
- **Dependency Injection**: NestJS DI container
- **Repository Pattern**: Data access abstraction
- **Decorator-based Routing**: `@MessageHandler` for MQTT topics
- **Layered Architecture**: Gateway → Routing → Business → Data Access