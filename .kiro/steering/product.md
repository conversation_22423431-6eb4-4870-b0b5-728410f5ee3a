# Product Overview

## Logic Handler Service (逻辑处理服务)

A real-time event processing service that serves as the core "brain" of a competitive event system. This service handles all business logic for live competitions including scoring, timing, flow control, and state management.

## Core Responsibilities

- **Message Processing**: Subscribe to MQTT topics and route commands/events to appropriate handlers
- **Business Logic Execution**: Process scoring, timing, player state management, and competition flow control
- **Real-time State Management**: Maintain live competition state in Redis cache
- **Data Persistence**: Store event logs and results in NocoDB database
- **Result Broadcasting**: Publish processed results back to MQTT for client consumption

## Key Features

- **Event-driven Architecture**: Asynchronous MQTT-based message processing
- **Dual Scoring Systems**: Automatic grading for objective questions, manual grading for subjective questions
- **Multi-session Support**: Handle different competition phases (Q&A, judging, PK battles, sprint rounds)
- **Real-time Leaderboards**: Live ranking updates with detailed score breakdowns
- **Robust State Management**: Player elimination tracking, revival chances, session transitions
- **Audit Trail**: Complete logging of all operations for transparency and debugging

## Target Performance

- Response time: <200ms (P99)
- High concurrency support for live competitions
- Real-time synchronization across multiple client types (control panel, displays, contestant devices)