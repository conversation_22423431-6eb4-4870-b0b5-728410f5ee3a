# Topic 设计

### **一、Topic 核心结构**

方案采用五层分层结构来定义所有消息（指令和事件）的意图，确保了通信的清晰性和可扩展性。 

`{eventid}/{domain}/{context}/{target}/{action}`

- **eventid**: 赛事的唯一标识符。通过这个字段，可以确保所有与特定赛事相关的通信都在一个独立的通道中进行，避免了多场赛事同时进行时的数据串扰。
- **domain**: 消息的领域。这个层级主要用于区分消息的大类，主要包括：
    - `command`: 由控制端（如中控、主持端）发出的控制指令。
    - `event`: 由客户端（如选手端、评委端）主动上报的事件。
    - `result`: 由后端服务（如云函数）处理完成后下发的结果数据。
- **context**: 指令或事件发生的具体上下文。例如 `system` (系统级控制), `quiz` (答题流程), `display` (大屏显示), `judge` (评委评分), `timer` (计时器) 等。
- **target**: 操作或事件所影响的具体目标对象。例如 `player` (选手端), `screen` (大屏), `all` (所有终端), `question` (题目), `rank` (排行榜) 等。
- **action**: 描述具体执行的动作。例如 `refresh` (刷新), `start` (开始), `submit` (提交), `show` (显示), `switch` (切换), `update` (更新) 等。

### **二、Topic 与 Payload 详细列表**

以下是根据您的设计文档，将所有指令和事件进行分类整合后的完整列表。

---

### **1. 系统控制 (System Control)**

由中控端发起，用于控制整个系统的基本状态。

| 指令/事件描述 | 发起方 | 推荐 Topic 结构 | Payload 示例 |
| --- | --- | --- | --- |
| 刷新选手端 | 中控端 | `{eventid}/command/system/player/refresh` | `{"user_id": 1001}` |
| 刷新大屏端 | 中控端 | `{eventid}/command/system/screen/refresh` | `{}` |
| 刷新评委端 | 中控端 | `{eventid}/command/system/judge/refresh` | `{}` |
| 刷新主持端 | 中控端 | `{eventid}/command/system/host/refresh` | `{}` |
| 让所有设备回到主页 | 中控端 | `{eventid}/command/system/all/go_home` | `{}` |
| 加载指定赛事 | 中控端 | `system/bootstrap/assign_event` | `{"eventid": "pwfhg8yj7eb4rh2"}` |
| 重载指定环节的配置 | 中控端 | `{eventid}/command/system/config/reload` | `{"session_id": "pk_final_debate"}` **说明**: 用于通知逻辑服务重新从 NocoDB 加载并缓存指定环节的配置。 |
| 刷新指定赛事的全部缓存 | 中控端 | `{eventid}/command/system/cache/refresh_all` | `{}`
它将清除并从NocoDB重载该赛事的所有缓存数据，并触发分数和排行榜的重新计算。 |

---

### **2. 赛事与答题流程 (Quiz Flow & Control)**

主要由中控端发起，用于控制答题环节的进程。

| 指令/事件描述 | 发起方 | 推荐 Topic 结构 | Payload 示例 |
| --- | --- | --- | --- |
| 进入某个环节 | 中控端 | `{eventid}/command/quiz/session/start` | `{"session_id": "qa_mandatory_01", "session_name": "有问必答环节必答题阶段"}` |
| 切换到指定题目 | 中控端 | `{eventid}/command/quiz/question/switch` | `{"question_id": 285, "question_number": 1}` |
| 要求所有选手提交答案 | 中控端 | `{eventid}/command/quiz/answer/submit_now` | `{}` |
| 更新题目数据 | 逻辑处理服务 | `{eventid}/result/quiz/question/update` | `{
  "question_id": 285,
  "question_number": 1,
  "question_type": "单选题",
  "prompt": "每年（ ）为全民国家安全教育日。",
  "options": [
    { "key": "A", "text": "12月4日" },
    { "key": "B", "text": "4月15日" },
    { "key": "C", "text": "3月15日" },
    { "key": "D", "text": "10月1日" }
  ],
  "correct_answer": "B",
  "points": 10,
  "explanation": "根据《中华人民共和国国家安全法》，自2016年起，每年4月15日为全民国家安全教育日。",
  "attachment_url": "",
  "session_id": "qa_mandatory_01",
  "stage": "必答题"
}` |
| **选手提交答案 (事件)** | **选手端** | `{eventid}/event/answer/submit` | `{"user_id": 1001, "session_id": "qa_mandatory_01", "question_id": 281, "question_number": 1, "submitted_answer": "D"}` |
| **评委提交评分 (事件)** | **评委端** | `{eventid}/event/judge/submit` | `{"judge_id": 2001, "user_id": 1001, "judge_score": 98.5, "session_id": "qa_mandatory_01"}`
**说明:** 此事件是**幂等**的。在打分通道关闭前，同一位评委 (`judge_id`) 对同一位选手 (`user_id`) 的多次提交，后续提交将覆盖前一次的评分。这是实现“分数修正”功能的基础。 |
| 返回已判分的最终结果 | 逻辑处理服务 | `{eventid}/result/answer/grading/update` | `{"user_id": 1001, "session_id": "qa_mandatory_01", "question_id": 281, "question_number": 1, "submitted_answer": "D", "grading_type": "人工判分","is_correct":1,"score":10}` |
| 返回错误处理信息 | 逻辑处理服务 | `{eventid}/result/system/error/notify` | `{
"timestamp": "2025-07-23T12:30:00.123Z",
"level": "error",
"error_code": "RESOURCE_NOT_FOUND",
"message": "指令执行失败：找不到指定的资源。",
"details": {
"source_topic": "{eventid}/command/quiz/question/switch",
"source_payload": {
"question_id": 999
},
"suggestion": "请确认您操作的题目ID是否正确，或检查题库中是否存在该题目。"
}
}` |
| 推送人工判分待办任务 | 逻辑处理服务 | `{eventid}/event/adjudication/task/new` | `{
"submission_id": 12345, // 对应“答题记录表”中的 id
"user_id": 1001,
"user_name": "海事大学", // 冗余字段，方便前端直接显示
"question_id": 282,
"question_prompt": "请填写我国第一艘航空母舰的名称。", // 冗余字段，方便前端显示
"submitted_answer": "data:image/png" // 需要被评审的内容
}` |
| 更新选手状态（复活机会） | 逻辑处理服务 | `{eventid}/result/player/status/update` | `{"user_id": 1001, "user_name": "复旦大学", "is_correct": 0, "revival_chances": 0, "status": "active" }` 
``**说明:**  
``- `revival_chances`: 选手剩余的复活次数。
``- `status`: 选手当前状态，例如 `active` (活跃), `eliminated` (已淘汰，当复活次数小于0时)。 |
| 赛事加载完成 | 逻辑处理服务 | `{eventid}/result/system/loaded` | `{
"event_id": "pwfhg8yj7eb4rh2",
"event_name": "2025年全国大学生国家安全知识竞赛",
"status": "ready",
"timestamp": "2025-07-23T12:30:00.123Z",
"preloaded_items": {
"players": 12,
"questions": 85,
"sessions": 5
 }
}`
**说明:**`event_name`: 赛事的完整名称，方便前端直接显示确认。
`status`: 明确的状态标识，`"ready"` 表示后端服务已完成所有准备工作。 `preloaded_items`: 包含了预热到 Redis 的核心数据统计（选手数量、题目总数、环节数），这为前端提供了一个快速验证数据完整性的途径，也为调试和监控提供了便利。 |
| 单题作废 | 中控端 | `{eventid}/command/quiz/question/invalidate` | `{
"question_id": 126,
"reason": "C选项描述与事实不符，仲裁成功"
}` |
| **题包切换** | 中控端 | `{eventid}/command/quiz/pack/switch` | `{
  "session_id": "qa_mandatory_01",
  "target_pack_id": 2,
  "reason": "正式题包多道题目出现争议，切换至备用题包"
}` |
| **系统通知** | 逻辑处理服务 | `{eventid}/result/system/notification/show` | `{
"timestamp": "2025-07-25T16:05:00.000Z",
"level": "success",
"message": "仲裁成功：第 5 题及其得分已作废。",
"duration": 7000,
"display_style": "toast"
}`
用于评委提交不完整警告示例：
`{
  "timestamp": "2025-07-25T16:10:00.000Z",
  "level": "warning",
  "message": "警告：仅收到 5/7 位评委的评分，是否确认要强制计算最终结果？",
  "duration": 0, // 0表示不自动消失
  "display_style": "dialog", // 建议前端以对话框形式展示
  "action_required": true // 明确告知前端需要用户交互
}`

// 用于配置重载成功的示例
`{
"timestamp": "2025-07-28T14:30:00.000Z",
"level": "success",
"message": "环节 '终极PK' 的配置已成功重载。",
"duration": 5000,
"display_style": "toast"
}` |
| 题包切换回执 | 逻辑处理服务 | `{eventid}/result/quiz/pack/switched` | `{
  "session_id": "qa_mandatory_01",
  "active_pack_id": 2
}`

在新的拉取模式下，此 Topic 变得至关重要。它是通知所有客户端其本地题目缓存已失效，必须立即重新拉取新题包的权威信号。 |
| 挑战题数据校验 | **中控员** | `{eventid}/command/quiz/challenge/prepare` | `{"challenger_id": 1001, "challenged_id": 1005}` |

---

### **3. 大屏显示与交互控制 (Display & Interaction)**

由中控端发起，用于控制大屏端内容的展示。

| 指令/事件描述 | 发起方 | 推荐 Topic 结构 | Payload 示例 |
| --- | --- | --- | --- |
| 显示/隐藏排行榜 | 中控端 | `{eventid}/command/display/rank/show` | `{}` |
| 控制大屏显示特定内容 | 中控端 | `{eventid}/command/display/content/show` | `{"info_id": 1, "info_type": "规则介绍"}` |
| 更新大屏数据 | 逻辑处理服务 | `eventid}/result/display/content/update` | `{"info_id": 1, "title": "有问必答", "content": "本环节共**8道**题目...", "attachment_url": "..."}` |
| 控制大屏内容分页 | 中控端 | `{eventid}/command/display/content/paginate` | `{"page": 2}` |
| 显示正确答案 | 中控端 | `{eventid}/command/display/question/show_answer` | `{}` |
| 显示选手答案和正误 | 中控端 | `{eventid}/command/display/question/reveal_all` | `{}` |
| 显示题目答案解析 | 中控端 | `{eventid}/command/display/question/show_explanation` | `{}` |
| **排行榜更新 (结果)** | 逻辑处理服务 | `{eventid}/result/rank/all/update` | `{"timestamp": 1721542109, "leaderboard": [{"final_rank": 1, "user_id": 1001, "user_name": "海事大学","final_score": 330, "score_details": {"sessions":[{"session_id":"qa_mandatory_01","session_name":"有问必答","score":100,"correct_answers":10,"total_answers":10},{"session_id":"sprint_180","session_name":"争分夺秒","score":158,"time_used":"172s","correct_answers":28,"total_answers":30},{"session_id":"judge_final_pk","session_name":"终极PK评分","score":88.5,"judge_score":[90,88,87.5]}],"adjustments":[{"amount":-10,"reason":"技术犯规，扣除10分","operator":"admin_user"}]}` |
| **争分夺秒排行榜更新** | **逻辑处理服务** | `{eventid}/result/rank/sprint/update` | `{ "timestamp": 1721542109, "leaderboard": [ { "user_id": 1001, "user_name": "海事大学", "sprint_score": 150, "sprint_progress": 15, "sprint_correct_count": 15 }, { "user_id": 1002, "user_name": "复旦大学", "sprint_score": 120, "sprint_progress": 18, "sprint_correct_count": 12 } ] }`

**设计说明：**
• **专用性**: 此Topic仅用于“争分夺秒”环节，确保其他环节不受影响。
• **轻量化**: Payload中只包含本环节必要的数据（队伍ID、名称、当前得分、已答题数、答对题数），移除了复杂的`score_details`等全局数据，大幅减少了网络传输负担。 |

---

### **4. 特定环节控制 (Specific Session Control)**

### **4.1 填空题展示控制**

| 指令/事件描述 | 发起方 | 推荐 Topic 结构 | Payload 示例 |
| --- | --- | --- | --- |
| 显示放大选手作答 | 中控端 | `{eventid}/command/display/handwriting/show` | `{}` |
| 下一位 | 中控端 | `{eventid}/command/display/handwriting/next` | `{}` |
| 上一位 | 中控端 | `{eventid}/command/display/handwriting/prev` | `{}` |
| 跳转到某一位选手 | 中控端 | `{eventid}/command/display/handwriting/jump` | `{"user_id": 1002}` |

### **4.2 “争分夺秒”环节**

| 指令/事件描述 | 发起方 | 推荐 Topic 结构 | Payload 示例 |
| --- | --- | --- | --- |
| 比赛计时开始 | 中控端 | `{eventid}/command/timer/race/start` | `{"duration": 180}`

**关于题目获取**: “争分夺秒”环节的题目数据不通过 MQTT Topic 进行推送。其采用“HTTP拉取”模式，客户端通过调用 `GET /api/v1/events/{eventId}/sessions/{sessionId}/questions` 接口一次性获取，以实现最低的答题延迟。相关的 MQTT Topic（如 `{eventid}/command/quiz/session/start`）仅作为触发此拉取动作的信号。 |
| 比赛计时重置 | 中控端 | `{eventid}/command/timer/race/reset` | `{}` |
| 实时排行榜分页 | 中控端 | `{eventid}/command/display/rank/paginate` | `{"page": 2}` |
| 争分夺秒”环节计时器状态 | 逻辑处理服务 | `{eventid}/result/timer/race/update` | `{ "session_id": "sprint_180", "status": "running", "duration": 180, "remaining_time": 172 }`

说明: 
 - `status`: 计时器当前状态，选项包括 `running` (运行中), `paused` (暂停), `finished` (已结束)。 
- `duration`: 本环节设定的总时长（单位：秒）。
- `remaining_time`: 当前精确的剩余时间（单位：秒）。 |
| **计算最终得分** | 中控端 | `{eventid}/command/quiz/sprint/finalize_score` | `{}` |

### **4.3 “安全公开课”环节**

| 指令/事件描述 | 发起方 | 推荐 Topic 结构 | Payload 示例 |
| --- | --- | --- | --- |
| 开启评委打分 | 中控端 | `{eventid}/command/judge/entry/start` | `{"user_id": 1001}` |
| **评委实时打分提交** | **逻辑处理服务** | `{eventid}/result/judge/score/submitted` | `{
"judge_id": 2001,
 "judge_name": "评委 1",
 "user_id": 1001,
 "judge_score": 98.5
}` |
| 关闭评委打分 | 中控端 | `{eventid}/command/judge/entry/end` | `{}` |
| 结束评分并计算 | 中控端 | `{eventid}/command/judge/score/calculate` | `{}` 或 `{"force": true}` 
**说明**: 默认无Payload。当需要强制执行不完整评分的计算时，Payload中需包含`"force": true`。 |
| 显示计算后最终评分评委计算后的最终赋分 | 中控端 | `{eventid}/command/display/rank/show_final` | `{}` |
| 返回评委计算后的最终赋分 | 逻辑处理服务 | `{eventid}/result/judge/score/update` | `{ "user_id": 1001, "session_id": "judge_final_pk", "final_judge_score": 88.5, "judge_scores_raw": [90, 88, 87.5] }` |
| **安全公开课环节排行榜更新**  | **逻辑处理服务** | `{eventid}/result/rank/judge_session/update` | `{"leaderboard": [ { "user_id": 1001, "user_name": "复旦大学", "session_score": 88.5 }, { "user_id": 1002, "user_name": "海事大学", "session_score": 85.0 } ] }` |
| 对环节内排行进行最终排序 | 中控端 | `{eventid}/command/display/rank/sort_now` | `{ "session_id": "security_open_class" }`

**说明:** 此指令用于通知前端对特定环节的排行榜进行界面排序，后端服务不处理。 |

### **4.4 “终极PK”环节**

| 指令/事件描述 | 发起方 | 推荐 Topic 结构 | Payload 示例 & 说明 |
| --- | --- | --- | --- |
| 切换计时阶段 | 中控端 | `{eventid}/command/pk/phase/switch` | `{"phase_id": "opening_statement"}` |
| 更新计时状态 | 逻辑处理服务 | `{eventid}/result/timer/pk_state/update` | `{
"global_status": "running",
"active_team": "pro_team",
"pro_team": {
"remaining_time": 90
},
"con_team": {
"remaining_time": 90
},
"current_phase": {
"phase_id": "opening_statement",
"phase_name": "观点陈述", // 从数据库查出的名称
"duration": 90, // 从数据库查出的总时长
"remaining_time": 90, // 当前阶段的剩余时间
"reminder_at": 15 // 从数据库查出的提醒阈值
,"is_reminding": true  //当其值为 true 时，明确表示当前时刻已达到提醒阈值，前端应立即触发提醒效果。在提醒点过后，后续的更新消息中此值将变回 false
}
}` |
| 正方计时器控制 | 中控端 | `{eventid}/command/timer/pro_team/control` | `{"status": "start"}` 
// **Status 选项:** start, pause, reset |
| 反方计时器控制 | 中控端 | `{eventid}/command/timer/con_team/control` | `{"status": "start"}`
**Status 选项:** start, pause, reset |
| 全局计时器控制 | 中控端 | `{eventid}/command/timer/global/control` | {"status": "pause"} |
| 切换发言方 | 中控端 | `{eventid}/command/pk/turn/switch` | `{"active_team": "pro_team"}` active_team **选项:** pro_team (正方), con_team (反方) |
| 开启投票入口 | 中控端 | `{eventid}/command/pk/vote/start` | `{"pro_team_id": 1001, "con_team_id": 1002, "pro_team_name": "A队", "con_team_name": "B队"}` |
| 提交投票数据 | 评委端/观众端 | `{eventid}/event/pk/vote/submit`  | **Payload (专家):** `{"voter_id": 2001, "voter_type": "expert", "voted_for_team_id": 1001}`
**Payload (大众):** `{"voter_id": "device_vwxyz", "voter_type": "public", "voted_for_team_id": 1002}`
**说明:** 此事件为**一次性提交**，服务后端会进行严格的幂等性处理。**一旦首次投票成功，后续来自同一 `voter_id` 的重复提交将被自动忽略，不允许改票**。 |
| 更新实时票数 | 逻辑处理服务 | `{eventid}/result/pk/vote_progress/update` | `{
  "pro_team": {
    "total_votes": 12,
    "total_score": 32,
    "expert_votes": 4,
    "expert_score": 20,
    "public_votes": 8,
    "public_score": 12
  },
  "con_team": {
    "total_votes": 9,
    "total_score": 29,
    "expert_votes": 5,
    "expert_score": 25,
    "public_votes": 4,
    "public_score": 4
  }
}` |
| 关闭投票入口 | 中控端 | `{eventid}/command/pk/vote/end`  | `{}` |
| **计算并裁定最终冠军** | 中控端 | `{eventid}/command/pk/result/calculate` | `{}` |

---

### **5. 通用计时器控制**

| 指令/事件描述 | 发起方 | 推荐 Topic 结构 | Payload 示例 |
| --- | --- | --- | --- |
| 控制大屏开始倒计时 | 中控端 | `{eventid}/command/timer/screen/start` | `{"duration": 10}` |