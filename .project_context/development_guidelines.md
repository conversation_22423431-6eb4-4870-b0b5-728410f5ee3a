# 开发指南 - 逻辑处理服务

## 1. 开发流程

### 1.1 TDD开发循环
1. **需求分析**: 理解业务需求和验收标准
2. **测试设计**: 编写测试用例（Red阶段）
3. **接口设计**: 定义函数签名和数据结构
4. **最小实现**: 编写最少代码使测试通过（Green阶段）
5. **重构优化**: 改进代码质量（Refactor阶段）
6. **集成验证**: 确保模块间正确交互
7. **文档更新**: 更新API文档和使用说明

### 1.2 分支管理
- **主分支**: `main` - 生产就绪代码
- **开发分支**: `develop` - 集成开发代码
- **功能分支**: `feature/功能名` - 新功能开发
- **修复分支**: `bugfix/问题描述` - Bug修复
- **发布分支**: `release/版本号` - 发布准备

### 1.3 代码审查
- **必须审查**: 所有PR必须经过代码审查
- **审查要点**: 代码质量、测试覆盖率、性能影响
- **自动检查**: ESLint、Prettier、测试通过

## 2. 模块开发规范

### 2.1 消息处理器开发
```typescript
// 1. 定义消息类型
export interface QuestionSwitchPayload {
  question_id: number;
  question_number: number;
}

// 2. 编写测试用例
describe('QuestionSwitchHandler', () => {
  it('should switch question successfully', async () => {
    // Given
    const payload: QuestionSwitchPayload = {
      question_id: 281,
      question_number: 1
    };
    
    // When
    const result = await handler.handleQuestionSwitch(payload);
    
    // Then
    expect(result.success).toBe(true);
    expect(mockRedis.hset).toHaveBeenCalledWith(
      'event:test:state',
      'current_question_id',
      281
    );
  });
});

// 3. 实现处理器
@Injectable()
export class QuestionSwitchHandler {
  @MessageHandler('command/quiz/question/switch')
  async handleQuestionSwitch(payload: QuestionSwitchPayload) {
    // 实现逻辑
  }
}
```

### 2.2 服务层开发
```typescript
// 1. 定义接口
export interface IScoringService {
  autoGrading(submission: AnswerSubmission): Promise<GradingResult>;
  calculateJudgeScore(scores: JudgeScore[]): Promise<FinalScore>;
}

// 2. 编写测试
describe('ScoringService', () => {
  beforeEach(() => {
    // 测试前置条件
  });
  
  it('should calculate correct score', async () => {
    // 测试逻辑
  });
});

// 3. 实现服务
@Injectable()
export class ScoringService implements IScoringService {
  constructor(
    private readonly redisService: RedisService,
    private readonly nocodbService: NocoDBService,
  ) {}
  
  async autoGrading(submission: AnswerSubmission): Promise<GradingResult> {
    // 实现逻辑
  }
}
```

### 2.3 数据访问层开发
```typescript
// 1. 定义数据模型
export interface PlayerState {
  user_id: number;
  user_name: string;
  revival_chances: number;
  status: 'active' | 'eliminated';
}

// 2. 编写Repository
@Injectable()
export class PlayerRepository {
  constructor(private readonly redis: Redis) {}
  
  async getPlayerState(eventId: string, userId: number): Promise<PlayerState> {
    const key = `event:${eventId}:player:${userId}`;
    const data = await this.redis.hgetall(key);
    return this.transformToPlayerState(data);
  }
  
  async updatePlayerState(eventId: string, userId: number, state: Partial<PlayerState>) {
    const key = `event:${eventId}:player:${userId}`;
    await this.redis.hmset(key, state);
  }
}
```

## 3. 测试开发规范

### 3.1 单元测试规范
```typescript
describe('ComponentName', () => {
  let service: ServiceClass;
  let mockDependency: jest.Mocked<DependencyClass>;
  
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ServiceClass,
        {
          provide: DependencyClass,
          useValue: createMockDependency(),
        },
      ],
    }).compile();
    
    service = module.get<ServiceClass>(ServiceClass);
    mockDependency = module.get(DependencyClass);
  });
  
  describe('methodName', () => {
    it('should handle normal case', async () => {
      // Given-When-Then模式
    });
    
    it('should handle edge case', async () => {
      // 边界情况测试
    });
    
    it('should handle error case', async () => {
      // 异常情况测试
    });
  });
});
```

### 3.2 集成测试规范
```typescript
describe('Integration: AnswerSubmissionFlow', () => {
  let app: INestApplication;
  let redis: Redis;
  let nocodb: NocoDBService;
  
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
    .overrideProvider(RedisService)
    .useValue(createTestRedis())
    .compile();
    
    app = moduleFixture.createNestApplication();
    await app.init();
  });
  
  it('should process answer submission end-to-end', async () => {
    // 端到端测试逻辑
  });
});
```

### 3.3 性能测试规范
```typescript
describe('Performance: MessageProcessing', () => {
  it('should process messages within 200ms', async () => {
    const startTime = Date.now();
    
    await messageHandler.process(testPayload);
    
    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(200);
  });
  
  it('should handle concurrent requests', async () => {
    const promises = Array(100).fill(null).map(() => 
      messageHandler.process(testPayload)
    );
    
    const results = await Promise.all(promises);
    expect(results.every(r => r.success)).toBe(true);
  });
});
```

## 4. 错误处理规范

### 4.1 异常分类
```typescript
// 业务异常
export class BusinessException extends Error {
  constructor(message: string, public code: string) {
    super(message);
  }
}

// 系统异常
export class SystemException extends Error {
  constructor(message: string, public cause?: Error) {
    super(message);
  }
}

// 验证异常
export class ValidationException extends Error {
  constructor(public errors: ValidationError[]) {
    super('Validation failed');
  }
}
```

### 4.2 全局异常过滤器
```typescript
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    
    if (exception instanceof BusinessException) {
      return response.status(400).json({
        success: false,
        error: {
          code: exception.code,
          message: exception.message
        }
      });
    }
    
    // 其他异常处理
  }
}
```

## 5. 性能优化指南

### 5.1 缓存策略
- **预热缓存**: 赛事开始前预加载题目信息
- **分层缓存**: L1内存 + L2Redis + L3NocoDB
- **缓存失效**: 基于TTL和事件驱动的失效机制

### 5.2 数据库优化
- **连接池**: 复用数据库连接
- **批量操作**: 减少网络往返次数
- **索引优化**: 确保查询性能

### 5.3 并发处理
- **异步处理**: 非关键路径异步执行
- **队列机制**: 使用Redis队列处理耗时任务
- **限流保护**: 防止系统过载

## 6. 监控和日志

### 6.1 日志规范
```typescript
// 结构化日志
logger.info('Answer submitted', {
  eventId: 'test-event',
  userId: 1001,
  questionId: 281,
  isCorrect: true,
  processingTime: 45
});

// 错误日志
logger.error('Failed to update leaderboard', {
  eventId: 'test-event',
  userId: 1001,
  error: error.message,
  stack: error.stack
});
```

### 6.2 指标收集
```typescript
// 业务指标
metrics.counter('answer_submissions_total').inc();
metrics.histogram('grading_duration_ms').observe(duration);
metrics.gauge('active_players_count').set(playerCount);

// 性能指标
metrics.histogram('http_request_duration_ms').observe(duration);
metrics.counter('redis_operations_total').inc();
```

## 7. 部署和运维

### 7.1 环境配置
- **开发环境**: 本地开发和测试
- **测试环境**: 集成测试和性能测试
- **生产环境**: 正式运行环境

### 7.2 健康检查
```typescript
@Get('health')
@HealthCheck()
check() {
  return this.health.check([
    () => this.redis.pingCheck('redis'),
    () => this.nocodb.pingCheck('nocodb'),
    () => this.mqtt.pingCheck('mqtt'),
  ]);
}
```

### 7.3 容器化部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1
CMD ["node", "dist/main"]
```
