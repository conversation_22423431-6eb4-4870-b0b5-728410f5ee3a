# API接口设计文档

## 1. 接口设计原则

### 1.1 RESTful设计
- **资源导向**: URL表示资源，HTTP方法表示操作
- **无状态**: 每个请求包含完整信息
- **统一接口**: 标准的HTTP状态码和响应格式
- **分层系统**: 清晰的分层架构

### 1.2 响应格式标准
```typescript
// 成功响应
interface SuccessResponse<T> {
  success: true;
  data: T;
  timestamp: string;
}

// 错误响应
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

## 2. 核心API端点

### 2.1 EMQX Webhook接入
```typescript
@Post('/api/v1/hooks/emqx')
@ApiOperation({ summary: 'EMQX Webhook接入点' })
async handleEmqxWebhook(@Body() payload: EmqxWebhookPayload[]) {
  // 处理EMQX推送的消息
  return { success: true };
}

interface EmqxWebhookPayload {
  topic: string;
  payload: string; // JSON字符串，需要解析
  clientid: string;
  timestamp: number;
}
```

### 2.2 赛事状态管理
```typescript
@Get('/api/v1/events/:eventId/state')
@ApiOperation({ summary: '获取赛事实时状态' })
async getEventState(@Param('eventId') eventId: string) {
  const state = await this.stateService.getEventState(eventId);
  return { success: true, data: state };
}

interface EventState {
  event_id: string;
  current_session: string;
  current_question_id: number;
  timer_status: 'running' | 'paused' | 'stopped';
  active_players: number;
}
```

### 2.3 排行榜管理
```typescript
@Get('/api/v1/events/:eventId/leaderboard')
@ApiOperation({ summary: '获取排行榜' })
async getLeaderboard(@Param('eventId') eventId: string) {
  const leaderboard = await this.scoringService.getLeaderboard(eventId);
  return { success: true, data: leaderboard };
}

@Post('/api/v1/events/:eventId/scores/recalculate')
@ApiOperation({ summary: '重新计算排行榜' })
async recalculateScores(@Param('eventId') eventId: string) {
  await this.scoringService.recalculateAllScores(eventId);
  return { success: true, data: { message: '排行榜重算完成' } };
}
```

### 2.4 人工判分接口
```typescript
@Post('/api/v1/grade/manual')
@ApiOperation({ summary: '提交人工判分结果' })
async submitManualGrading(@Body() payload: ManualGradingPayload) {
  const result = await this.scoringService.processManualGrading(payload);
  return { success: true, data: result };
}

interface ManualGradingPayload {
  id: number;           // 答题记录表ID
  is_correct: number;   // 0或1
  score: number;        // 得分
  grader_id?: string;   // 评分员ID
}
```

### 2.5 分数调整接口
```typescript
@Post('/api/v1/events/:eventId/players/:userId/adjust-score')
@ApiOperation({ summary: '手动调整选手分数' })
async adjustPlayerScore(
  @Param('eventId') eventId: string,
  @Param('userId') userId: number,
  @Body() payload: ScoreAdjustmentPayload
) {
  const result = await this.scoringService.adjustScore(eventId, userId, payload);
  return { success: true, data: result };
}

interface ScoreAdjustmentPayload {
  adjustment: number;   // 调整分数（可为负数）
  reason: string;       // 调整原因
  operator_id: string;  // 操作员ID
}
```

## 3. 数据传输对象 (DTOs)

### 3.1 消息处理DTOs
```typescript
// 答案提交DTO
export class AnswerSubmitDto {
  @IsNumber()
  user_id: number;

  @IsString()
  session_id: string;

  @IsNumber()
  question_id: number;

  @IsNumber()
  question_number: number;

  @IsString()
  submitted_answer: string;
}

// 题目切换DTO
export class QuestionSwitchDto {
  @IsNumber()
  question_id: number;

  @IsNumber()
  question_number: number;
}

// 评委评分DTO
export class JudgeSubmitDto {
  @IsNumber()
  judge_id: number;

  @IsNumber()
  user_id: number;

  @IsNumber()
  @Min(0)
  @Max(100)
  judge_score: number;

  @IsString()
  session_id: string;
}
```

### 3.2 响应DTOs
```typescript
// 判分结果DTO
export class GradingResultDto {
  user_id: number;
  session_id: string;
  question_id: number;
  question_number: number;
  submitted_answer: string;
  grading_type: '自动判分' | '人工判分';
  is_correct: number;
  score: number;
  timestamp: string;
}

// 排行榜DTO
export class LeaderboardDto {
  timestamp: string;
  leaderboard: PlayerRankDto[];
}

export class PlayerRankDto {
  rank: number;
  user_id: number;
  user_name: string;
  final_score: number;
  score_details: ScoreDetailsDto;
}

export class ScoreDetailsDto {
  sessions: SessionScoreDto[];
  adjustments: AdjustmentDto[];
}
```

## 4. 错误处理

### 4.1 错误码定义
```typescript
export enum ErrorCode {
  // 通用错误
  INVALID_PARAMETER = 'INVALID_PARAMETER',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  
  // 业务错误
  EVENT_NOT_LOADED = 'EVENT_NOT_LOADED',
  QUESTION_NOT_FOUND = 'QUESTION_NOT_FOUND',
  PLAYER_NOT_FOUND = 'PLAYER_NOT_FOUND',
  INVALID_SESSION_STATE = 'INVALID_SESSION_STATE',
  
  // 数据错误
  REDIS_CONNECTION_ERROR = 'REDIS_CONNECTION_ERROR',
  NOCODB_API_ERROR = 'NOCODB_API_ERROR',
  MQTT_CONNECTION_ERROR = 'MQTT_CONNECTION_ERROR',
}
```

### 4.2 异常处理器
```typescript
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const status = exception.getStatus();
    
    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        code: this.getErrorCode(exception),
        message: exception.message,
      },
      timestamp: new Date().toISOString(),
    };
    
    response.status(status).json(errorResponse);
  }
}
```

## 5. 认证和授权

### 5.1 API令牌认证
```typescript
@Injectable()
export class ApiTokenGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const token = request.headers['authorization']?.replace('Bearer ', '');
    
    if (!token) {
      throw new UnauthorizedException('Missing API token');
    }
    
    return this.validateToken(token);
  }
  
  private validateToken(token: string): boolean {
    // 验证令牌逻辑
    return token === process.env.API_TOKEN;
  }
}
```

### 5.2 权限控制
```typescript
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}
  
  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    
    if (!requiredRoles) {
      return true;
    }
    
    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}
```

## 6. API文档生成

### 6.1 Swagger配置
```typescript
// main.ts
const config = new DocumentBuilder()
  .setTitle('逻辑处理服务 API')
  .setDescription('实时赛事系统核心逻辑处理服务API文档')
  .setVersion('1.0')
  .addBearerAuth()
  .build();

const document = SwaggerModule.createDocument(app, config);
SwaggerModule.setup('api/docs', app, document);
```

### 6.2 API装饰器使用
```typescript
@Controller('events')
@ApiTags('赛事管理')
@ApiBearerAuth()
export class EventController {
  @Get(':eventId/state')
  @ApiOperation({ summary: '获取赛事状态' })
  @ApiParam({ name: 'eventId', description: '赛事ID' })
  @ApiResponse({ status: 200, description: '成功', type: EventStateDto })
  @ApiResponse({ status: 404, description: '赛事不存在' })
  async getEventState(@Param('eventId') eventId: string) {
    // 实现逻辑
  }
}
```

## 7. 性能优化

### 7.1 缓存策略
```typescript
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const cacheKey = this.generateCacheKey(request);
    
    // 检查缓存
    const cachedResult = this.cacheService.get(cacheKey);
    if (cachedResult) {
      return of(cachedResult);
    }
    
    // 执行请求并缓存结果
    return next.handle().pipe(
      tap(result => this.cacheService.set(cacheKey, result, 300))
    );
  }
}
```

### 7.2 限流保护
```typescript
@Injectable()
export class ThrottlerGuard extends ThrottlerGuard {
  protected getTracker(req: Record<string, any>): string {
    return req.ip; // 基于IP限流
  }
}

// 使用示例
@UseGuards(ThrottlerGuard)
@Throttle(10, 60) // 每分钟最多10次请求
@Post('hooks/emqx')
async handleWebhook() {
  // 处理逻辑
}
```

## 8. 监控和指标

### 8.1 健康检查端点
```typescript
@Get('health')
@HealthCheck()
check() {
  return this.health.check([
    () => this.redis.pingCheck('redis'),
    () => this.nocodb.pingCheck('nocodb'),
    () => this.mqtt.pingCheck('mqtt'),
  ]);
}
```

### 8.2 指标收集
```typescript
@Injectable()
export class MetricsInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const start = Date.now();
    const request = context.switchToHttp().getRequest();
    
    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - start;
        this.metricsService.recordHttpRequest(
          request.method,
          request.route.path,
          200,
          duration
        );
      }),
      catchError(error => {
        const duration = Date.now() - start;
        this.metricsService.recordHttpRequest(
          request.method,
          request.route.path,
          error.status || 500,
          duration
        );
        throw error;
      })
    );
  }
}
```
