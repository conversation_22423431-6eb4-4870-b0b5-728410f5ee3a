# 逻辑处理服务架构设计

## 1. 架构概述

### 1.1 设计原则
- **事件驱动**: 基于MQTT消息的异步处理
- **微服务化**: 模块化设计，便于扩展和维护
- **无状态**: 所有状态存储在外部系统（Redis/NocoDB）
- **最终一致性**: 跨系统数据同步保证
- **高性能**: P99延迟<200ms的实时响应

### 1.2 技术栈选择
- **框架**: NestJS + Fastify
- **语言**: TypeScript
- **消息**: MQTT (EMQX)
- **缓存**: Redis
- **数据库**: NocoDB
- **监控**: OpenTelemetry + Prometheus
- **测试**: Jest + TDD

## 2. 分层架构设计

### 2.1 接入层 (Gateway Layer)
**职责**: 外部请求接入和协议转换
- **HTTP API**: 接收EMQX Webhook和管理接口
- **MQTT Client**: 订阅消息和发布结果
- **认证中间件**: API令牌验证
- **限流中间件**: 防止系统过载

### 2.2 路由层 (Routing Layer)
**职责**: 消息路由和分发
- **消息路由器**: 基于Topic结构的智能路由
- **装饰器系统**: `@MessageHandler` 声明式路由
- **类型验证**: Payload结构验证
- **错误处理**: 统一异常处理机制

### 2.3 业务处理层 (Business Layer)
**职责**: 核心业务逻辑实现
- **指令处理器**: 处理command域消息
- **事件处理器**: 处理event域消息
- **计分引擎**: 自动/人工判分逻辑
- **计时器管理**: 多种计时器并发管理
- **状态机**: 业务状态转换管理

### 2.4 数据访问层 (Data Access Layer)
**职责**: 数据存储和缓存管理
- **Redis服务**: 实时状态缓存
- **NocoDB服务**: 持久化数据操作
- **状态管理器**: 跨系统状态同步
- **缓存策略**: 多级缓存和失效机制

## 3. 核心组件设计

### 3.1 消息处理组件
```typescript
@Injectable()
export class MessageRouter {
  @MessageHandler('command/quiz/question/switch')
  async handleQuestionSwitch(payload: QuestionSwitchPayload) {
    // 题目切换逻辑
  }
  
  @MessageHandler('event/answer/submit')
  async handleAnswerSubmit(payload: AnswerSubmitPayload) {
    // 答案提交处理
  }
}
```

### 3.2 状态管理组件
```typescript
@Injectable()
export class StateManager {
  async updatePlayerState(eventId: string, userId: number, state: PlayerState) {
    // 状态更新逻辑
  }
  
  async getEventState(eventId: string): Promise<EventState> {
    // 赛事状态查询
  }
}
```

### 3.3 计分引擎组件
```typescript
@Injectable()
export class ScoringEngine {
  async autoGrading(submission: AnswerSubmission): Promise<GradingResult> {
    // 自动判分逻辑
  }
  
  async calculateJudgeScore(scores: JudgeScore[]): Promise<FinalScore> {
    // 评委分数计算
  }
}
```

## 4. 数据模型设计

### 4.1 Redis缓存结构
```
event:{eventid}:leaderboard          # 总排行榜 (Sorted Set)
event:{eventid}:player:{userId}      # 选手状态 (Hash)
event:{eventid}:state                # 赛事状态 (Hash)
event:{eventid}:questions            # 题目缓存 (Hash)
event:{eventid}:timers:{timerId}     # 计时器状态 (Hash)
```

### 4.2 消息类型定义
```typescript
interface AnswerSubmitPayload {
  user_id: number;
  session_id: string;
  question_id: number;
  question_number: number;
  submitted_answer: string;
}

interface QuestionSwitchPayload {
  question_id: number;
  question_number: number;
}
```

## 5. 性能优化策略

### 5.1 缓存策略
- **L1缓存**: 内存缓存（题目信息、配置）
- **L2缓存**: Redis缓存（实时状态）
- **L3存储**: NocoDB（持久化数据）

### 5.2 并发处理
- **连接池**: Redis和NocoDB连接复用
- **批处理**: 非关键路径异步处理
- **预计算**: 题目答案预热到缓存

### 5.3 精度保证
- **Decimal.js**: 避免浮点数精度问题
- **原子操作**: Redis原子命令保证一致性
- **事务处理**: 关键操作的事务保证

## 6. 监控和可观测性

### 6.1 指标监控
- **业务指标**: 消息处理速率、判分准确率
- **性能指标**: 响应延迟、吞吐量
- **系统指标**: CPU、内存、网络使用率

### 6.2 链路追踪
- **请求追踪**: 从消息接收到结果广播的完整链路
- **性能分析**: 识别性能瓶颈
- **错误定位**: 快速定位问题根因

### 6.3 日志管理
- **结构化日志**: JSON格式便于查询
- **分级记录**: INFO/WARN/ERROR分级
- **审计日志**: 关键操作完整记录
