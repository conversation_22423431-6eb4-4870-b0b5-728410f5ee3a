# 技术栈配置指南

## 1. 核心技术栈

### 1.1 运行时环境
- **Node.js**: v18+ (LTS版本)
- **TypeScript**: v5.0+
- **包管理器**: npm 或 pnpm

### 1.2 核心框架
- **NestJS**: v10+ (微服务框架)
- **Fastify**: v4+ (HTTP服务器，性能优化)
- **Class-validator**: 数据验证
- **Class-transformer**: 数据转换

### 1.3 消息和通信
- **MQTT.js**: v5+ (MQTT客户端)
- **Axios**: HTTP客户端
- **Socket.io**: WebSocket支持（可选）

### 1.4 数据存储
- **Redis**: v7+ (缓存和状态存储)
- **ioredis**: Redis客户端库
- **NocoDB SDK**: 数据库操作

### 1.5 工具库
- **Decimal.js**: 高精度数值计算
- **Lodash**: 工具函数库
- **Moment.js/Day.js**: 时间处理
- **UUID**: 唯一标识符生成

## 2. 开发工具

### 2.1 测试框架
- **Jest**: v29+ (单元测试和集成测试)
- **Supertest**: HTTP接口测试
- **@nestjs/testing**: NestJS测试工具
- **Redis-memory-server**: Redis测试环境

### 2.2 代码质量
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **Husky**: Git钩子管理
- **Lint-staged**: 暂存文件检查

### 2.3 监控和日志
- **Winston**: 结构化日志
- **@opentelemetry/api**: 链路追踪
- **Prometheus**: 指标收集
- **@nestjs/terminus**: 健康检查

## 3. 项目结构

### 3.1 推荐目录结构
```
logic-handler-service/
├── src/
│   ├── common/              # 公共模块
│   │   ├── decorators/      # 自定义装饰器
│   │   ├── filters/         # 异常过滤器
│   │   ├── guards/          # 守卫
│   │   ├── interceptors/    # 拦截器
│   │   └── pipes/           # 管道
│   ├── config/              # 配置模块
│   ├── modules/             # 业务模块
│   │   ├── message/         # 消息处理模块
│   │   ├── scoring/         # 计分模块
│   │   ├── timer/           # 计时器模块
│   │   ├── state/           # 状态管理模块
│   │   └── cache/           # 缓存模块
│   ├── shared/              # 共享模块
│   │   ├── types/           # 类型定义
│   │   ├── constants/       # 常量定义
│   │   └── utils/           # 工具函数
│   ├── app.module.ts        # 应用主模块
│   └── main.ts              # 应用入口
├── test/                    # 测试文件
│   ├── unit/                # 单元测试
│   ├── integration/         # 集成测试
│   └── e2e/                 # 端到端测试
├── docs/                    # 文档
├── docker/                  # Docker配置
├── .env.example             # 环境变量示例
├── package.json
├── tsconfig.json
├── nest-cli.json
└── jest.config.js
```

### 3.2 模块设计原则
- **单一职责**: 每个模块专注一个业务领域
- **依赖注入**: 使用NestJS的DI容器
- **接口隔离**: 定义清晰的模块接口
- **开闭原则**: 对扩展开放，对修改封闭

## 4. 配置管理

### 4.1 环境变量配置
```bash
# .env
NODE_ENV=development
PORT=3000

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# NocoDB配置
NOCODB_BASE_URL=https://noco.ohvfx.com
NOCODB_API_TOKEN=your_api_token_here

# MQTT配置
MQTT_BROKER_URL=mqtt://localhost:1883
MQTT_USERNAME=
MQTT_PASSWORD=

# 监控配置
PROMETHEUS_PORT=9090
LOG_LEVEL=info
```

### 4.2 配置模块设计
```typescript
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        NODE_ENV: Joi.string().valid('development', 'production', 'test'),
        PORT: Joi.number().default(3000),
        REDIS_HOST: Joi.string().required(),
        NOCODB_API_TOKEN: Joi.string().required(),
      }),
    }),
  ],
})
export class AppModule {}
```

## 5. 依赖管理

### 5.1 核心依赖 (package.json)
```json
{
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "@nestjs/core": "^10.0.0",
    "@nestjs/platform-fastify": "^10.0.0",
    "@nestjs/config": "^3.0.0",
    "@nestjs/schedule": "^3.0.0",
    "fastify": "^4.0.0",
    "mqtt": "^5.0.0",
    "ioredis": "^5.0.0",
    "axios": "^1.0.0",
    "decimal.js": "^10.0.0",
    "class-validator": "^0.14.0",
    "class-transformer": "^0.5.0",
    "winston": "^3.0.0"
  },
  "devDependencies": {
    "@nestjs/testing": "^10.0.0",
    "@types/jest": "^29.0.0",
    "jest": "^29.0.0",
    "supertest": "^6.0.0",
    "eslint": "^8.0.0",
    "prettier": "^3.0.0",
    "husky": "^8.0.0",
    "lint-staged": "^13.0.0"
  }
}
```

### 5.2 开发脚本
```json
{
  "scripts": {
    "build": "nest build",
    "start": "nest start",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:e2e": "jest --config ./test/jest-e2e.json",
    "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
    "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\""
  }
}
```

## 6. 开发环境配置

### 6.1 TypeScript配置 (tsconfig.json)
```json
{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2020",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@/*": ["src/*"],
      "@/common/*": ["src/common/*"],
      "@/modules/*": ["src/modules/*"]
    }
  }
}
```

### 6.2 Jest测试配置 (jest.config.js)
```javascript
module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: 'src',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    '**/*.(t|j)s',
    '!**/*.spec.ts',
    '!**/node_modules/**',
  ],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
  },
};
```

## 7. 部署配置

### 7.1 Docker配置
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/main"]
```

### 7.2 健康检查
```typescript
@Controller('health')
export class HealthController {
  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.redis.pingCheck('redis'),
      () => this.nocodb.pingCheck('nocodb'),
    ]);
  }
}
```
