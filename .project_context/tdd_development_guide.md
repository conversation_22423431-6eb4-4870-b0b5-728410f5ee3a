# TDD开发指南 - 逻辑处理服务

## 1. TDD开发流程

### 1.1 Red-Green-Refactor循环
1. **Red**: 编写失败的测试用例
2. **Green**: 编写最少代码使测试通过
3. **Refactor**: 重构代码，保持测试通过

### 1.2 测试优先级
1. **核心业务逻辑**: 计分、判题、状态管理
2. **消息处理**: MQTT消息路由和处理
3. **数据一致性**: Redis和NocoDB同步
4. **性能要求**: 响应时间和并发处理

## 2. 测试分层策略

### 2.1 单元测试 (Unit Tests)
**目标**: 测试单个函数/方法的逻辑正确性
**工具**: Jest + TypeScript
**覆盖率要求**: >90%

**测试示例**:
```typescript
describe('ScoringEngine', () => {
  describe('autoGrading', () => {
    it('should return correct score for right answer', async () => {
      // Given
      const submission = {
        question_id: 1,
        submitted_answer: 'B',
        user_id: 1001
      };
      
      // When
      const result = await scoringEngine.autoGrading(submission);
      
      // Then
      expect(result.is_correct).toBe(1);
      expect(result.score).toBe(10);
    });
  });
});
```

### 2.2 集成测试 (Integration Tests)
**目标**: 测试组件间交互和外部系统集成
**范围**: Redis操作、NocoDB API调用、MQTT消息处理

**测试示例**:
```typescript
describe('AnswerSubmissionFlow', () => {
  it('should process answer submission end-to-end', async () => {
    // Given
    const mockPayload = {
      user_id: 1001,
      question_id: 281,
      submitted_answer: 'D'
    };
    
    // When
    await messageHandler.handleAnswerSubmit(mockPayload);
    
    // Then
    const playerState = await redis.hgetall('event:test:player:1001');
    expect(playerState.score).toBe('10');
    
    const dbRecord = await nocodb.getAnswerRecord(mockPayload);
    expect(dbRecord.is_correct).toBe(1);
  });
});
```

### 2.3 端到端测试 (E2E Tests)
**目标**: 测试完整的业务流程
**范围**: 从MQTT消息接收到结果广播的完整链路

### 2.4 性能测试 (Performance Tests)
**目标**: 验证性能要求
**指标**: 响应时间<200ms，并发处理能力

## 3. 测试数据管理

### 3.1 测试数据策略
- **隔离性**: 每个测试使用独立的数据集
- **可重复性**: 测试结果可重复
- **清理机制**: 测试后自动清理数据

### 3.2 Mock策略
```typescript
// Redis Mock
const mockRedis = {
  hget: jest.fn(),
  hset: jest.fn(),
  zincrby: jest.fn()
};

// NocoDB Mock
const mockNocoDB = {
  createRecord: jest.fn(),
  updateRecord: jest.fn(),
  queryRecords: jest.fn()
};
```

## 4. 测试用例设计模板

### 4.1 业务逻辑测试模板
```typescript
describe('BusinessLogicComponent', () => {
  beforeEach(() => {
    // 测试前置条件
  });
  
  afterEach(() => {
    // 测试清理
  });
  
  describe('核心功能', () => {
    it('正常情况下应该...', () => {
      // Given-When-Then模式
    });
    
    it('边界情况下应该...', () => {
      // 边界值测试
    });
    
    it('异常情况下应该...', () => {
      // 异常处理测试
    });
  });
});
```

### 4.2 消息处理测试模板
```typescript
describe('MessageHandler', () => {
  it('should handle valid message correctly', async () => {
    // Given
    const validPayload = createValidPayload();
    
    // When
    const result = await handler.process(validPayload);
    
    // Then
    expect(result).toBeDefined();
    expect(mockRedis.hset).toHaveBeenCalled();
  });
  
  it('should reject invalid message format', async () => {
    // Given
    const invalidPayload = createInvalidPayload();
    
    // When & Then
    await expect(handler.process(invalidPayload))
      .rejects.toThrow('Invalid payload format');
  });
});
```

## 5. 测试环境配置

### 5.1 测试配置
```typescript
// test.config.ts
export const testConfig = {
  redis: {
    host: 'localhost',
    port: 6379,
    db: 1 // 使用独立的测试数据库
  },
  nocodb: {
    baseUrl: 'http://localhost:8080',
    token: 'test-token'
  }
};
```

### 5.2 CI/CD集成
```yaml
# .github/workflows/test.yml
name: Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis:7
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:integration
      - run: npm run test:e2e
```

## 6. 测试驱动的开发流程

### 6.1 功能开发流程
1. **需求分析**: 明确功能需求和验收标准
2. **测试设计**: 编写测试用例（先不实现）
3. **接口设计**: 定义函数签名和数据结构
4. **测试实现**: 实现测试用例（应该失败）
5. **功能实现**: 编写最少代码使测试通过
6. **重构优化**: 改进代码质量，保持测试通过
7. **集成测试**: 验证组件间交互
8. **文档更新**: 更新API文档和使用说明

### 6.2 Bug修复流程
1. **重现Bug**: 编写能重现Bug的测试用例
2. **确认失败**: 验证测试用例确实失败
3. **修复代码**: 修改代码使测试通过
4. **回归测试**: 确保修复没有引入新问题

## 7. 质量保证

### 7.1 代码覆盖率
- **单元测试**: >90%
- **集成测试**: >80%
- **关键路径**: 100%

### 7.2 测试质量指标
- **测试执行时间**: 单元测试<5s，集成测试<30s
- **测试稳定性**: 无随机失败
- **测试可读性**: 清晰的测试描述和断言

### 7.3 持续改进
- **定期审查**: 每周测试用例审查
- **性能监控**: 测试执行时间趋势
- **覆盖率跟踪**: 代码覆盖率变化监控
