# 项目上下文目录

此目录用于存储逻辑处理服务项目的持久化上下文信息，作为AI助手和开发团队的唯一可信信息源。

## 目录结构

- `README.md` - 本文件，项目上下文说明
- `project_overview.md` - 项目总体概述
- `architecture.md` - 系统架构设计
- `tech_stack.md` - 技术栈选择和配置
- `development_guidelines.md` - 开发规范和指南

## 项目基本信息

**项目名称**: 逻辑处理服务 (Logic Handler Service)
**项目类型**: 实时赛事系统核心后端服务
**技术栈**: Node.js + TypeScript + NestJS + Fastify
**架构模式**: 事件驱动 + MQTT + Redis + NocoDB

## 核心职责

- 消息订阅与路由（EMQX MQTT）
- 业务逻辑处理（计分、计时、流程控制）
- 实时状态管理（Redis缓存）
- 数据持久化（NocoDB）
- 结果广播（MQTT发布）

## 文档状态

✅ 完整的PRD文档
✅ Topic设计规范  
✅ NocoDB数据表设计
✅ API交互指南
❌ 代码库结构分析（待补充）
❌ 开发环境配置（待补充）
❌ 实现计划（待制定）
